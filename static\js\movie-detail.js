/**
 * 电影详情页面JavaScript模块
 * 负责处理电影详情的加载、显示和NFO同步功能
 */

class MovieDetailManager {
    constructor() {
        this.accessType = null;
        this.movieId = null;
        this.num = null;
        
        // DOM元素缓存
        this.elements = {
            loadingContainer: null,
            errorContainer: null,
            detailContainer: null,
            refreshBtn: null,
            editBtn: null,
            syncNfoBtn: null
        };
        
        this.init();
    }
    
    /**
     * 初始化电影详情管理器
     */
    init() {
        // 缓存DOM元素
        this.cacheElements();
        
        // 绑定事件监听器
        this.bindEvents();
    }
    
    /**
     * 设置页面参数
     */
    setPageParams(accessType, movieId, num) {
        this.accessType = accessType;
        this.movieId = movieId;
        this.num = num;
        
        // 根据参数加载电影详情
        this.loadInitialData();
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        this.elements = {
            loadingContainer: document.getElementById('loading-container'),
            errorContainer: document.getElementById('error-container'),
            detailContainer: document.getElementById('movie-detail-container'),
            refreshBtn: document.getElementById('refresh-btn'),
            editBtn: document.getElementById('edit-btn'),
            syncNfoBtn: document.getElementById('sync-nfo-btn')
        };
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 刷新按钮事件
        if (this.elements.refreshBtn) {
            this.elements.refreshBtn.addEventListener('click', () => {
                this.handleRefresh();
            });
        }
        
        // 编辑按钮事件
        if (this.elements.editBtn) {
            this.elements.editBtn.addEventListener('click', () => {
                this.handleEdit();
            });
        }
        
        // NFO同步按钮事件
        if (this.elements.syncNfoBtn) {
            this.elements.syncNfoBtn.addEventListener('click', () => {
                this.handleNfoSync();
            });
        }
    }
    
    /**
     * 加载初始数据
     */
    loadInitialData() {
        if (this.accessType === 'id' && this.movieId) {
            this.loadMovieDetailById(this.movieId);
        } else if (this.accessType === 'num' && this.num) {
            this.loadMovieDetailByNum(this.num);
        }
    }
    
    /**
     * 处理刷新按钮点击
     */
    handleRefresh() {
        if (this.accessType === 'id' && this.movieId) {
            this.loadMovieDetailById(this.movieId);
        } else if (this.accessType === 'num' && this.num) {
            this.loadMovieDetailByNum(this.num);
        }
    }
    
    /**
     * 处理编辑按钮点击
     */
    handleEdit() {
        if (this.accessType === 'id' && this.movieId) {
            window.location.href = `/movies/id/${this.movieId}/edit`;
        } else if (this.accessType === 'num' && this.num) {
            window.location.href = `/movies/${this.num}/edit`;
        }
    }
    
    /**
     * 处理NFO同步按钮点击
     */
    handleNfoSync() {
        if (this.accessType === 'id' && this.movieId) {
            this.showNFOSyncModal(this.movieId);
        } else if (this.accessType === 'num' && this.num) {
            // 需要先获取影片ID
            this.getMovieIdByNum(this.num).then(movieId => {
                if (movieId) {
                    this.showNFOSyncModal(movieId);
                }
            });
        }
    }
    
    /**
     * 根据ID加载电影详情
     */
    async loadMovieDetailById(id) {
        await this.loadMovieDetail(`/api/movies/id/${id}`);
    }
    
    /**
     * 根据编号加载电影详情
     */
    async loadMovieDetailByNum(num) {
        // 首先通过num获取movie ID，然后使用ID接口
        try {
            const movieId = await this.getMovieIdByNum(num);
            if (movieId) {
                await this.loadMovieDetailById(movieId);
            } else {
                throw new Error('无法找到对应的影片');
            }
        } catch (error) {
            console.error('通过编号加载影片详情失败:', error);
            this.showErrorState(error.message);
        }
    }
    
    /**
     * 加载电影详情的通用方法
     */
    async loadMovieDetail(apiUrl) {
        // 显示加载状态
        this.showLoadingState();
        
        try {
            const response = await fetch(apiUrl);
            const result = await response.json();
            
            if (result.success && result.data) {
                this.displayMovieDetail(result.data);
                this.showDetailState();
            } else {
                throw new Error(result.message || '获取影片详情失败');
            }
        } catch (error) {
            console.error('Error loading movie detail:', error);
            this.showErrorState(error.message);
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoadingState() {
        this.elements.loadingContainer?.classList.remove('hidden');
        this.elements.errorContainer?.classList.add('hidden');
        this.elements.detailContainer?.classList.add('hidden');
    }
    
    /**
     * 显示详情状态
     */
    showDetailState() {
        this.elements.loadingContainer?.classList.add('hidden');
        this.elements.errorContainer?.classList.add('hidden');
        this.elements.detailContainer?.classList.remove('hidden');
    }
    
    /**
     * 显示错误状态
     */
    showErrorState(message) {
        const errorMessageElement = document.getElementById('error-message');
        if (errorMessageElement) {
            errorMessageElement.textContent = message;
        }
        
        this.elements.loadingContainer?.classList.add('hidden');
        this.elements.errorContainer?.classList.remove('hidden');
        this.elements.detailContainer?.classList.add('hidden');
    }
    
    /**
     * 根据编号获取电影ID
     */
    async getMovieIdByNum(num) {
        try {
            // 使用影片列表API搜索指定num的影片
            const response = await fetch('/api/movies', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    limit: 1,
                    offset: 0,
                    search: num  // 通过搜索功能查找num
                })
            });

            const data = await response.json();

            if (data.success && data.data && data.data.length > 0) {
                // 在搜索结果中查找精确匹配的num
                const movie = data.data.find(m => m.num === num);
                if (movie) {
                    return movie.id;
                } else {
                    if (window.toast) {
                        window.toast.error('未找到匹配的影片编号');
                    }
                    return null;
                }
            } else {
                if (window.toast) {
                    window.toast.error('未找到对应的影片');
                }
                return null;
            }
        } catch (error) {
            console.error('根据编号获取影片ID失败:', error);
            if (window.toast) {
                window.toast.error('获取影片信息失败');
            }
            return null;
        }
    }

    /**
     * 显示电影详情信息
     */
    displayMovieDetail(movie) {
        // 设置页面标题
        this.setPageTitle(movie);

        // 设置海报
        this.setPosterImage(movie);

        // 设置基本信息
        this.setBasicInfo(movie);

        // 设置分类、系列、标签、演员
        this.setCategories(movie);

        // 设置剧情简介和文件路径
        this.setAdditionalInfo(movie);

        // 创建收藏按钮
        this.createFavoriteButton(movie.id);

        // 显示操作按钮
        this.showActionButtons();
    }

    /**
     * 设置页面标题
     */
    setPageTitle(movie) {
        const titleElement = document.getElementById('movie-title');
        const detailTitleElement = document.getElementById('detail-movie-title');

        if (titleElement) titleElement.textContent = movie.title;
        if (detailTitleElement) detailTitleElement.textContent = movie.title;

        document.title = `${movie.title} - 影片详情 - 媒体管理器`;

        // 设置原始标题
        const originalTitleElement = document.getElementById('movie-original-title');
        if (originalTitleElement && movie.original_title && movie.original_title !== movie.title) {
            originalTitleElement.textContent = movie.original_title;
        }
    }

    /**
     * 设置海报图片
     */
    setPosterImage(movie) {
        const posterImg = document.getElementById('movie-poster');
        if (!posterImg) return;

        if (movie.poster_uuid) {
            posterImg.src = `/api/images/${movie.poster_uuid}`;
            posterImg.alt = movie.title;
            posterImg.classList.remove('hidden');
        } else {
            posterImg.classList.add('hidden');
        }
    }

    /**
     * 设置基本信息
     */
    setBasicInfo(movie) {
        // 年份
        const yearElement = document.getElementById('movie-year');
        if (yearElement) yearElement.textContent = movie.year || '-';

        // 评分
        const ratingElement = document.getElementById('movie-rating');
        if (ratingElement) {
            ratingElement.textContent = movie.rating ? movie.rating.toFixed(1) : '-';
        }

        // 时长
        const runtimeElement = document.getElementById('movie-runtime');
        if (runtimeElement) {
            runtimeElement.textContent = movie.runtime ? this.formatRuntime(movie.runtime) : '-';
        }

        // 国家/地区
        const countryElement = document.getElementById('movie-country');
        if (countryElement) countryElement.textContent = movie.country || '-';
    }

    /**
     * 设置分类、系列、标签、演员信息
     */
    setCategories(movie) {
        // 设置分类
        this.setGenres(movie.genres);

        // 设置系列
        this.setSeries(movie.series);

        // 设置标签
        this.setTags(movie.tags);

        // 设置演员
        this.setActors(movie.actors);
    }

    /**
     * 设置分类信息
     */
    setGenres(genres) {
        const genresContainer = document.getElementById('movie-genres');
        if (!genresContainer) return;

        if (genres && genres.length > 0) {
            genresContainer.innerHTML = genres.map(genre =>
                `<span class="badge badge-primary">${genre.name}</span>`
            ).join('');
        } else {
            genresContainer.innerHTML = '<span class="text-base-content/60">-</span>';
        }
    }

    /**
     * 设置系列信息
     */
    setSeries(series) {
        const seriesElement = document.getElementById('movie-series');
        if (seriesElement) {
            seriesElement.textContent = series ? series.name : '-';
        }
    }

    /**
     * 设置标签信息
     */
    setTags(tags) {
        const tagsContainer = document.getElementById('movie-tags');
        if (!tagsContainer) return;

        if (tags && tags.length > 0) {
            tagsContainer.innerHTML = tags.map(tag =>
                `<span class="badge badge-secondary">${tag.name}</span>`
            ).join('');
        } else {
            tagsContainer.innerHTML = '<span class="text-base-content/60">-</span>';
        }
    }

    /**
     * 设置演员信息
     */
    setActors(actors) {
        const actorsContainer = document.getElementById('movie-actors');
        if (!actorsContainer) return;

        if (actors && actors.length > 0) {
            actorsContainer.innerHTML = actors.map(actor =>
                `<span class="badge badge-info">${actor.name}</span>`
            ).join('');
        } else {
            actorsContainer.innerHTML = '<span class="text-base-content/60">-</span>';
        }
    }

    /**
     * 设置附加信息（剧情简介和文件路径）
     */
    setAdditionalInfo(movie) {
        // 设置剧情简介
        const plotElement = document.getElementById('movie-plot');
        if (plotElement) {
            plotElement.textContent = movie.plot || '暂无剧情简介';
        }

        // 设置文件路径
        const filePathElement = document.getElementById('movie-file-path');
        if (filePathElement) {
            filePathElement.textContent = movie.file_path || '-';
        }
    }

    /**
     * 显示操作按钮
     */
    showActionButtons() {
        this.elements.editBtn?.classList.remove('hidden');
        this.elements.syncNfoBtn?.classList.remove('hidden');
    }

    /**
     * 格式化时长显示
     */
    formatRuntime(minutes) {
        if (!minutes) return '-';
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}小时${mins}分钟`;
        } else {
            return `${mins}分钟`;
        }
    }

    /**
     * 显示NFO同步对话框
     */
    showNFOSyncModal(movieId) {
        const modal = document.getElementById('nfo-sync-modal');
        const content = document.getElementById('nfo-sync-content');
        const footer = document.getElementById('nfo-sync-footer');

        if (!modal || !content || !footer) return;

        // 重置对话框状态
        content.innerHTML = `
            <div class="flex justify-center">
                <span class="loading loading-spinner loading-lg text-primary"></span>
            </div>
            <p class="text-center mt-4">正在检查NFO文件状态...</p>
        `;
        footer.classList.add('hidden');

        modal.showModal();

        // 检查NFO状态
        this.checkNFOStatus(movieId, content, footer);
    }

    /**
     * 检查NFO文件状态
     */
    async checkNFOStatus(movieId, content, footer) {
        try {
            const response = await fetch(`/api/movies/id/${movieId}/nfo-status`);
            const data = await response.json();

            if (data.success) {
                const nfoData = data.data;

                if (!nfoData.can_sync) {
                    this.showCannotSyncMessage(content, footer, nfoData);
                } else {
                    this.showCanSyncMessage(content, footer, nfoData, movieId);
                }

                footer.classList.remove('hidden');
            } else {
                this.showNFOError(content, footer, data.message);
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNFOError(content, footer, '网络错误，请稍后重试');
        }
    }

    /**
     * 显示无法同步的消息
     */
    showCannotSyncMessage(content, footer, nfoData) {
        content.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="警告图标"></i>
                <div>
                    <h3 class="font-bold">无法同步到NFO文件</h3>
                    <div class="text-xs">${nfoData.reason}</div>
                    ${nfoData.nfo_path ? `<div class="text-xs mt-1 opacity-70">NFO路径: ${nfoData.nfo_path}</div>` : ''}
                </div>
            </div>
        `;
        footer.innerHTML = `
            <button type="button" class="btn btn-outline" onclick="document.getElementById('nfo-sync-modal').close()">关闭</button>
        `;
    }

    /**
     * 创建收藏按钮
     */
    async createFavoriteButton(movieId) {
        const container = document.getElementById('favorite-btn-container');
        if (!container || !window.favoriteManager) return;

        try {
            // 获取收藏状态
            const status = await window.favoriteManager.getFavoriteStatus(movieId);

            // 创建收藏按钮
            const buttonHtml = window.favoriteManager.createFavoriteButton(
                movieId,
                status.is_favorited,
                'sm'
            );

            container.innerHTML = buttonHtml;

        } catch (error) {
            console.error('创建收藏按钮失败:', error);
            // 创建默认按钮
            const buttonHtml = window.favoriteManager.createFavoriteButton(movieId, false, 'sm');
            container.innerHTML = buttonHtml;
        }
    }

    /**
     * 显示可以同步的消息
     */
    showCanSyncMessage(content, nfoData, movieId) {
        content.innerHTML = `
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="信息图标"></i>
                <div>
                    <h3 class="font-bold">准备同步到NFO文件</h3>
                    <div class="text-xs">将会把数据库中的影片信息同步到NFO文件中。</div>
                    <div class="text-xs mt-2"><strong>NFO文件路径:</strong></div>
                    <code class="text-xs bg-base-200 px-2 py-1 rounded mt-1 block">${nfoData.nfo_path}</code>
                </div>
            </div>
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="警告图标"></i>
                <div>
                    <h3 class="font-bold">注意事项</h3>
                    <ul class="text-xs list-disc list-inside mt-1">
                        <li>同步将采用增量更新方式，仅更新已修改的字段</li>
                        <li>NFO文件中的其他信息将被保留</li>
                        <li>建议在同步前确认数据库信息正确</li>
                    </ul>
                </div>
            </div>
        `;

        // 设置确认按钮事件
        const confirmBtn = document.getElementById('confirm-sync-btn');
        if (confirmBtn) {
            confirmBtn.onclick = () => this.performNFOSync(movieId);
        }
    }

    /**
     * 显示NFO错误消息
     */
    showNFOError(content, footer, message) {
        content.innerHTML = `
            <div class="alert alert-error">
                <i class="bi bi-exclamation-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="错误图标"></i>
                <div>
                    <h3 class="font-bold">检查失败</h3>
                    <div class="text-xs">${message}</div>
                </div>
            </div>
        `;
        footer.innerHTML = `
            <button type="button" class="btn btn-outline" onclick="document.getElementById('nfo-sync-modal').close()">关闭</button>
        `;
        footer.classList.remove('hidden');
    }

    /**
     * 执行NFO同步
     */
    async performNFOSync(movieId) {
        const content = document.getElementById('nfo-sync-content');
        const footer = document.getElementById('nfo-sync-footer');

        if (!content || !footer) return;

        // 显示同步进度
        content.innerHTML = `
            <div class="flex justify-center">
                <span class="loading loading-spinner loading-lg text-primary"></span>
            </div>
            <p class="text-center mt-4">正在同步到NFO文件，请稍候...</p>
        `;
        footer.classList.add('hidden');

        try {
            const response = await fetch(`/api/movies/id/${movieId}/sync-nfo`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            // 关闭同步对话框
            document.getElementById('nfo-sync-modal')?.close();

            // 显示结果对话框
            this.showNFOSyncResult(data);
        } catch (error) {
            console.error('Error:', error);

            // 关闭同步对话框
            document.getElementById('nfo-sync-modal')?.close();

            // 显示错误结果
            this.showNFOSyncResult({
                success: false,
                message: '同步失败：网络错误，请稍后重试'
            });
        }
    }

    /**
     * 显示NFO同步结果
     */
    showNFOSyncResult(result) {
        const modal = document.getElementById('nfo-result-modal');
        const content = document.getElementById('nfo-result-content');

        if (!modal || !content) return;

        if (result.success) {
            const updatedFields = result.data?.updated_fields || [];
            const movieTitle = result.data?.movie_title || '影片';
            const nfoPath = result.data?.nfo_path || '';

            if (updatedFields.length > 0) {
                content.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="成功图标"></i>
                        <div>
                            <h3 class="font-bold">同步成功</h3>
                            <div class="text-xs">《${movieTitle}》的信息已成功同步到NFO文件。</div>
                            <div class="text-xs mt-2"><strong>更新的字段：</strong></div>
                            <div class="flex flex-wrap gap-1 mt-1">
                                ${updatedFields.map(field => `<span class="badge badge-primary badge-sm">${this.getFieldDisplayName(field)}</span>`).join('')}
                            </div>
                            <div class="text-xs mt-2"><strong>NFO文件路径：</strong></div>
                            <code class="text-xs bg-base-200 px-2 py-1 rounded mt-1 block">${nfoPath}</code>
                        </div>
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="信息图标"></i>
                        <div>
                            <h3 class="font-bold">无需更新</h3>
                            <div class="text-xs">《${movieTitle}》的NFO文件已是最新状态，无需更新。</div>
                        </div>
                    </div>
                `;
            }
        } else {
            content.innerHTML = `
                <div class="alert alert-error">
                    <i class="bi bi-exclamation-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="错误图标"></i>
                    <div>
                        <h3 class="font-bold">同步失败</h3>
                        <div class="text-xs">${result.message}</div>
                    </div>
                </div>
            `;
        }

        modal.showModal();
    }

    /**
     * 获取字段显示名称
     */
    getFieldDisplayName(field) {
        const fieldNames = {
            'title': '标题',
            'originaltitle': '原始标题',
            'year': '年份',
            'rating': '评分',
            'runtime': '时长',
            'plot': '剧情',
            'outline': '概要',
            'country': '国家',
            'criticrating': '影评评分',
            'sorttitle': '排序标题',
            'trailer': '预告片',
            'num': '编号',
            'lockdata': '锁定数据',
            'dateadded': '添加日期',
            'premiered': '首映日期',
            'releasedate': '发布日期',
            'genres': '分类',
            'tags': '标签',
            'actors': '演员',
            'series': '系列'
        };
        return fieldNames[field] || field;
    }
}

// 全局实例
let movieDetailManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    movieDetailManager = new MovieDetailManager();
});

// 导出供模板使用的函数
window.initMovieDetail = function(accessType, movieId, num) {
    if (movieDetailManager) {
        movieDetailManager.setPageParams(accessType, movieId, num);
    }
};
