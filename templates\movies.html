{% extends "base.html" %}

{% block title %}影片库 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-film text-primary" style="font-size: 2rem;" aria-label="影片库图标"></i>
                    影片库
                </h1>
                <p class="text-base-content/70 mt-2">浏览和管理您的影片收藏</p>
            </div>
            <div class="flex flex-wrap items-center gap-2 sm:gap-3">
                <button type="button" class="btn btn-outline btn-sm" id="view-toggle-btn" title="切换视图">
                    <i class="bi bi-grid" aria-label="视图切换图标"></i>
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="refresh-movies-btn">
                    <i class="bi bi-arrow-clockwise" aria-label="刷新图标"></i>
                    刷新
                </button>
                <button type="button" class="btn btn-outline btn-sm" id="batch-select-btn" title="批量选择">
                    <i class="bi bi-check-square" aria-label="批量选择图标"></i>
                    批量选择
                </button>
                <button type="button" class="btn btn-outline btn-sm" id="clear-filters-btn" title="清除筛选">
                    <i class="bi bi-arrow-clockwise" style="font-size: 0.75rem;" aria-label="清除筛选图标"></i>
                    清除筛选
                </button>
                <div class="dropdown dropdown-left sm:dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-outline btn-sm" title="排序">
                        <i class="bi bi-sort-down" style="font-size: 0.75rem;" aria-label="排序图标"></i>
                        排序
                        <i class="bi bi-chevron-down ml-1" style="font-size: 0.5rem;" aria-label="下拉图标"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-1 shadow-lg bg-base-100 rounded-box w-48 border border-base-300 text-sm max-w-[calc(100vw-2rem)] sm:max-w-none">
                        <li><a href="#" data-sort="title" class="flex items-center gap-2 py-2 whitespace-nowrap">
                            <i class="bi bi-sort-alpha-down" style="font-size: 0.75rem;" aria-label="标题排序图标"></i>
                            按标题排序
                        </a></li>
                        <li><a href="#" data-sort="year" class="flex items-center gap-2 py-2 whitespace-nowrap">
                            <i class="bi bi-calendar" style="font-size: 0.75rem;" aria-label="年份排序图标"></i>
                            按年份排序
                        </a></li>
                        <li><a href="#" data-sort="rating" class="flex items-center gap-2 py-2 whitespace-nowrap">
                            <i class="bi bi-star-fill" style="font-size: 0.75rem;" aria-label="评分排序图标"></i>
                            按评分排序
                        </a></li>
                        <li><a href="#" data-sort="created_at" class="flex items-center gap-2 py-2 whitespace-nowrap">
                            <i class="bi bi-clock" style="font-size: 0.75rem;" aria-label="时间排序图标"></i>
                            按添加时间排序
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Directory Filter Info -->
<div class="mb-4 hidden" id="directory-filter-info">
    <div class="alert alert-info">
        <div class="flex items-center gap-3">
            <i class="bi bi-folder" aria-label="目录图标"></i>
            <div class="flex-1">
                <span class="font-medium">当前筛选目录：</span>
                <span id="current-directory-name" class="font-semibold"></span>
                <span class="text-sm opacity-70 ml-2" id="current-directory-path"></span>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-outline btn-sm" id="show-all-directories-btn">
                    <i class="bi bi-list" aria-label="列表图标"></i>
                    显示所有目录
                </button>
                <button class="btn btn-ghost btn-sm" id="clear-directory-filter-btn">
                    <i class="bi bi-x" aria-label="清除图标"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-4">
    <div class="card-body p-4">
        <!-- 主搜索区域 -->
        <div class="flex flex-col lg:flex-row gap-3 items-end mb-4">
            <!-- 搜索输入框 -->
            <div class="form-control flex-1">
                <label class="label py-1">
                    <span class="label-text font-medium text-sm">搜索影片</span>
                    <span class="label-text-alt text-xs text-base-content/60">支持标题、演员、导演搜索</span>
                </label>
                <div class="join w-full">
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                        </div>
                        <input type="text"
                               class="input input-bordered w-full pl-9 pr-10 join-item focus:input-primary"
                               id="search-input"
                               placeholder="输入影片名称、演员或导演..."
                               autocomplete="off">
                        <button class="absolute inset-y-0 right-0 pr-2 flex items-center btn btn-ghost btn-xs btn-circle hidden"
                                type="button"
                                id="clear-search-btn"
                                title="清除搜索">
                            <i class="bi bi-x" style="font-size: 0.75rem;" aria-label="清除搜索图标"></i>
                        </button>
                    </div>
                    <button class="btn btn-primary join-item" type="button" id="search-btn">
                        <i class="bi bi-search" aria-label="搜索图标"></i>
                        搜索
                    </button>
                </div>
            </div>

            <!-- 高级搜索按钮 -->
            <div class="form-control">
                <label class="label py-1 lg:opacity-0">
                    <span class="label-text text-sm">　</span>
                </label>
                <button class="btn btn-outline" type="button" id="advanced-search-btn">
                    <i class="bi bi-funnel" aria-label="高级搜索图标"></i>
                    高级搜索
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Movies Grid -->
<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 md:gap-6" id="movies-container">
    <!-- Movies will be loaded here -->
</div>

<!-- Loading Skeleton -->
<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 md:gap-6 hidden" id="loading-skeleton">
    <!-- Skeleton cards will be generated here -->
</div>

<!-- Empty State -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="empty-state">
    <div class="bg-base-200 rounded-full p-6 mb-6">
        <i class="bi bi-film text-base-content/40" style="font-size: 4rem;" aria-label="空影片图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">没有找到影片</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md">尝试调整搜索条件或清除筛选器来查看更多影片</p>
    <button type="button" class="btn btn-primary btn-wide" id="empty-clear-filters-btn">
        <i class="bi bi-arrow-clockwise mr-2" aria-label="清除筛选图标"></i>
        清除筛选器
    </button>
</div>

<!-- Error State -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="error-state">
    <div class="bg-error/10 rounded-full p-6 mb-6">
        <i class="bi bi-exclamation-triangle text-error" style="font-size: 4rem;" aria-label="错误图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">加载失败</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md" id="error-message">无法加载影片数据，请稍后重试</p>
    <button type="button" class="btn btn-error btn-wide" id="error-retry-btn">
        <i class="bi bi-arrow-clockwise mr-2" aria-label="重试图标"></i>
        重试
    </button>
</div>

<!-- Pagination -->
<div class="flex flex-col md:flex-row justify-between items-center mt-8 gap-4" id="pagination-container">
    <div class="text-base-content/70" id="pagination-info">
        显示 0 - 0 条，共 0 条记录
    </div>
    <div class="join" id="pagination-controls">
        <!-- Pagination controls will be generated here -->
    </div>
</div>

<!-- Load More Button (for infinite scroll alternative) -->
<div class="flex justify-center mt-8 hidden" id="load-more-container">
    <button type="button" class="btn btn-outline btn-primary btn-wide" id="load-more-btn">
        <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
        <i class="bi bi-plus mr-2" aria-label="加载更多图标"></i>
        加载更多
    </button>
</div>

    <!-- Advanced Search Modal -->
    <dialog id="advanced-search-modal" class="modal overflow-visible">
        <div class="modal-box w-11/12 max-w-4xl overflow-visible">
            <form method="dialog">
                <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
            </form>
            <h3 class="font-bold text-xl mb-6">高级搜索</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 overflow-visible">
                <!-- 标签选择 -->
                <div class="form-control overflow-visible">
                    <label class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <i class="bi bi-tags text-primary" aria-label="标签图标"></i>
                            标签
                        </span>
                    </label>
                    <div id="tags-selector" class="overflow-visible"></div>
                </div>

                <!-- 分类选择 -->
                <div class="form-control overflow-visible">
                    <label class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <i class="bi bi-collection text-secondary" aria-label="分类图标"></i>
                            分类
                        </span>
                    </label>
                    <div id="genres-selector" class="overflow-visible"></div>
                </div>

                <!-- 系列选择 -->
                <div class="form-control overflow-visible">
                    <label class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <i class="bi bi-collection-play text-accent" aria-label="系列图标"></i>
                            系列
                        </span>
                    </label>
                    <div id="series-selector" class="overflow-visible"></div>
                </div>

                <!-- 年份和评分 -->
                <div class="space-y-4">
                    <!-- 年份选择 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium flex items-center gap-2">
                                <i class="bi bi-calendar text-warning" aria-label="日历图标"></i>
                                年份范围
                            </span>
                        </label>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="number" class="input input-bordered input-sm" id="year-from"
                                   placeholder="起始年份" min="1900" max="2100" step="1">
                            <input type="number" class="input input-bordered input-sm" id="year-to"
                                   placeholder="结束年份" min="1900" max="2100" step="1">
                        </div>
                    </div>

                    <!-- 评分选择 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium flex items-center gap-2">
                                <i class="bi bi-star-fill text-error" aria-label="星星图标"></i>
                                评分范围
                            </span>
                        </label>
                        <div class="grid grid-cols-2 gap-2">
                            <select class="select select-bordered select-sm" id="rating-from">
                                <option value="">最低评分</option>
                                <option value="1">1分以上</option>
                                <option value="2">2分以上</option>
                                <option value="3">3分以上</option>
                                <option value="4">4分以上</option>
                                <option value="5">5分以上</option>
                                <option value="6">6分以上</option>
                                <option value="7">7分以上</option>
                                <option value="8">8分以上</option>
                                <option value="9">9分以上</option>
                            </select>
                            <select class="select select-bordered select-sm" id="rating-to">
                                <option value="">最高评分</option>
                                <option value="10">10分以下</option>
                                <option value="9">9分以下</option>
                                <option value="8">8分以下</option>
                                <option value="7">7分以下</option>
                                <option value="6">6分以下</option>
                                <option value="5">5分以下</option>
                                <option value="4">4分以下</option>
                                <option value="3">3分以下</option>
                                <option value="2">2分以下</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-action mt-8">
                <button type="button" class="btn btn-outline" id="reset-advanced-search">
                    <i class="bi bi-arrow-clockwise mr-2" aria-label="重置图标"></i>
                    重置
                </button>
                <button type="button" class="btn btn-ghost" onclick="document.getElementById('advanced-search-modal').close()">取消</button>
                <button type="button" class="btn btn-primary" id="apply-advanced-search">
                    <i class="bi bi-funnel mr-2" aria-label="筛选图标"></i>
                    应用筛选
                </button>
            </div>
        </div>
    </dialog>

{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/multi-select-dropdown.js') }}"></script>
<script src="{{ url_for('static', path='/js/advanced-search.js') }}"></script>
<script src="{{ url_for('static', path='/js/favorite-manager.js') }}"></script>
<script src="{{ url_for('static', path='/js/batch-toolbar.js') }}"></script>
<script src="{{ url_for('static', path='/js/movies.js') }}"></script>
{% endblock %}
