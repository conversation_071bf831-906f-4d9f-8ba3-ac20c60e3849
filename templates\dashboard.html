{% extends "base.html" %}

{% block title %}仪表板 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-grid-3x3-gap text-primary" style="font-size: 2rem;" aria-label="仪表板图标"></i>
                    仪表板
                </h1>
                <p class="text-base-content/70 mt-2">媒体库概览和快速访问</p>
            </div>
            <div class="flex items-center gap-3">
                <!-- 刷新按钮 -->
                <button class="btn btn-outline btn-sm" id="refresh-dashboard" title="刷新数据">
                    <i class="bi bi-arrow-clockwise" aria-label="刷新图标"></i>
                    刷新
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 统计卡片区域 -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 影片数量卡片 -->
    <div class="stats shadow-lg border border-base-300 bg-base-100">
        <div class="stat">
            <div class="stat-figure text-primary">
                <i class="bi bi-film text-primary" style="font-size: 2rem;" aria-label="影片图标"></i>
            </div>
            <div class="stat-title">影片总数</div>
            <div class="stat-value text-primary" id="total-movies">-</div>
            <div class="stat-desc">媒体库中的影片</div>
        </div>
    </div>

    <!-- 标签数量卡片 -->
    <div class="stats shadow-lg border border-base-300 bg-base-100">
        <div class="stat">
            <div class="stat-figure text-secondary">
                <i class="bi bi-tags text-secondary" style="font-size: 2rem;" aria-label="标签图标"></i>
            </div>
            <div class="stat-title">标签总数</div>
            <div class="stat-value text-secondary" id="total-tags">-</div>
            <div class="stat-desc">分类标签</div>
        </div>
    </div>

    <!-- 分类数量卡片 -->
    <div class="stats shadow-lg border border-base-300 bg-base-100">
        <div class="stat">
            <div class="stat-figure text-accent">
                <i class="bi bi-collection text-accent" style="font-size: 2rem;" aria-label="分类图标"></i>
            </div>
            <div class="stat-title">分类总数</div>
            <div class="stat-value text-accent" id="total-genres">-</div>
            <div class="stat-desc">影片分类</div>
        </div>
    </div>

    <!-- 演员数量卡片 -->
    <div class="stats shadow-lg border border-base-300 bg-base-100">
        <div class="stat">
            <div class="stat-figure text-warning">
                <i class="bi bi-people text-warning" style="font-size: 2rem;" aria-label="演员图标"></i>
            </div>
            <div class="stat-title">演员总数</div>
            <div class="stat-value text-warning" id="total-actors">-</div>
            <div class="stat-desc">参演演员</div>
        </div>
    </div>
</div>

<!-- 媒体目录区域 -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-8">
    <div class="card-body">
        <div class="flex items-center justify-between mb-6">
            <h2 class="card-title text-xl font-bold flex items-center gap-2">
                <i class="bi bi-folder text-primary" aria-label="目录图标"></i>
                媒体目录
            </h2>
            <a href="/directories" class="btn btn-outline btn-sm">
                管理目录
                <i class="bi bi-gear ml-1" aria-label="设置图标"></i>
            </a>
        </div>

        <!-- 目录卡片网格 (与影片卡片保持一致的响应式布局) -->
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4" id="directories-grid">
            <!-- 目录卡片将在这里动态加载 -->
        </div>

        <!-- 加载状态 -->
        <div class="flex justify-center py-8" id="directories-loading">
            <span class="loading loading-spinner loading-lg text-primary"></span>
        </div>

        <!-- 空状态 -->
        <div class="text-center py-12 hidden" id="directories-empty">
            <i class="bi bi-folder-x text-base-content/30 mb-4" style="font-size: 4rem;" aria-label="空目录图标"></i>
            <p class="text-base-content/70 mb-4">暂无可用的媒体目录</p>
            <a href="/directories" class="btn btn-primary btn-sm">
                添加目录
            </a>
        </div>
    </div>
</div>

<!-- 最近添加的影片 -->
<div class="card bg-base-100 shadow-lg border border-base-300">
    <div class="card-body">
        <div class="flex items-center justify-between mb-6">
            <h2 class="card-title text-xl flex items-center gap-2">
                <i class="bi bi-clock-history text-primary" aria-label="时钟图标"></i>
                最近添加的影片
            </h2>
            <a href="/movies" class="btn btn-outline btn-sm">
                查看全部
                <i class="bi bi-arrow-right ml-1" aria-label="箭头图标"></i>
            </a>
        </div>

        <!-- 影片网格 -->
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4" id="recent-movies-grid">
            <!-- 最近影片将在这里动态加载 -->
        </div>

        <!-- 加载状态 -->
        <div class="flex justify-center py-8" id="recent-movies-loading">
            <span class="loading loading-spinner loading-lg text-primary"></span>
        </div>

        <!-- 空状态 -->
        <div class="text-center py-12 hidden" id="recent-movies-empty">
            <i class="bi bi-film text-base-content/30 mb-4" style="font-size: 4rem;" aria-label="空影片图标"></i>
            <p class="text-base-content/70">暂无影片数据</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>
{% endblock %}
