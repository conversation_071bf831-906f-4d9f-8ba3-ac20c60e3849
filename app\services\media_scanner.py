"""
媒体文件扫描器
用于扫描指定目录下的媒体文件并识别相关的元数据文件
"""
import os
from pathlib import Path
from typing import Dict, List, Optional, Set
import logging

logger = logging.getLogger(__name__)


class MediaScanner:
    """媒体文件扫描器类"""
    
    # 支持的视频文件扩展名
    VIDEO_EXTENSIONS = {'.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
    
    # 支持的图片文件扩展名
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp'}
    
    # 图片类型映射
    IMAGE_TYPES = {
        'poster': 'poster',
        'fanart': 'fanart', 
        'thumb': 'thumb',
        'banner': 'banner',
        'clearart': 'clearart',
        'landscape': 'landscape'
    }
    
    def __init__(self):
        self.scanned_files = []
    
    def scan_directory(self, directory_path: str, recursive: bool = True) -> List[Dict]:
        """
        扫描指定目录下的媒体文件
        
        Args:
            directory_path: 要扫描的目录路径
            recursive: 是否递归扫描子目录
            
        Returns:
            媒体文件信息列表
        """
        directory = Path(directory_path)
        if not directory.exists() or not directory.is_dir():
            logger.error(f"目录不存在或不是有效目录: {directory_path}")
            return []
        
        media_files = []
        
        try:
            if recursive:
                # 递归扫描所有子目录
                for root, dirs, files in os.walk(directory):
                    root_path = Path(root)
                    media_files.extend(self._scan_files_in_directory(root_path, files))
            else:
                # 只扫描当前目录
                files = [f.name for f in directory.iterdir() if f.is_file()]
                media_files.extend(self._scan_files_in_directory(directory, files))
                
        except Exception as e:
            logger.error(f"扫描目录时发生错误 {directory_path}: {e}")
        
        self.scanned_files = media_files
        return media_files
    
    def _scan_files_in_directory(self, directory: Path, files: List[str]) -> List[Dict]:
        """
        扫描单个目录中的文件
        
        Args:
            directory: 目录路径
            files: 文件名列表
            
        Returns:
            媒体文件信息列表
        """
        media_files = []
        
        # 首先找出所有视频文件
        video_files = []
        for file_name in files:
            file_path = directory / file_name
            if file_path.suffix.lower() in self.VIDEO_EXTENSIONS:
                video_files.append(file_name)
        
        # 为每个视频文件查找相关的元数据文件
        for video_file in video_files:
            media_info = self._analyze_media_file(directory, video_file, files)
            if media_info:
                media_files.append(media_info)
        
        return media_files
    
    def _analyze_media_file(self, directory: Path, video_file: str, all_files: List[str]) -> Optional[Dict]:
        """
        分析单个媒体文件及其相关文件
        
        Args:
            directory: 目录路径
            video_file: 视频文件名
            all_files: 目录中所有文件的列表
            
        Returns:
            媒体文件信息字典
        """
        video_path = directory / video_file
        base_name = Path(video_file).stem  # 不包含扩展名的文件名
        
        media_info = {
            'file_path': str(video_path),
            'file_name': video_file,
            'base_name': base_name,
            'directory': str(directory),
            'nfo_path': None,
            'images': {}
        }
        
        # 查找 NFO 文件
        nfo_file = f"{base_name}.nfo"
        if nfo_file in all_files:
            media_info['nfo_path'] = str(directory / nfo_file)
        
        # 查找相关图片文件
        media_info['images'] = self._find_related_images(directory, base_name, all_files)
        
        return media_info
    
    def _find_related_images(self, directory: Path, base_name: str, all_files: List[str]) -> Dict[str, str]:
        """
        查找与媒体文件相关的图片文件
        
        Args:
            directory: 目录路径
            base_name: 媒体文件的基础名称（不含扩展名）
            all_files: 目录中所有文件的列表
            
        Returns:
            图片类型到文件路径的映射
        """
        images = {}
        
        for file_name in all_files:
            file_path = Path(file_name)
            
            # 检查是否是图片文件
            if file_path.suffix.lower() not in self.IMAGE_EXTENSIONS:
                continue
            
            # 检查文件名是否以基础名称开头
            if not file_name.startswith(base_name):
                continue
            
            # 提取图片类型
            # 格式: base_name-type.ext 或 base_name.ext
            name_without_ext = file_path.stem
            if name_without_ext == base_name:
                # 如果文件名完全匹配，默认为 poster
                image_type = 'poster'
            elif name_without_ext.startswith(f"{base_name}-"):
                # 提取类型部分
                type_part = name_without_ext[len(base_name) + 1:]
                image_type = self.IMAGE_TYPES.get(type_part.lower(), type_part.lower())
            else:
                continue
            
            images[image_type] = str(directory / file_name)
        
        return images
    
    def get_scan_summary(self) -> Dict:
        """
        获取扫描结果摘要
        
        Returns:
            扫描结果摘要信息
        """
        if not self.scanned_files:
            return {
                'total_files': 0,
                'files_with_nfo': 0,
                'files_with_images': 0,
                'image_types_found': set()
            }
        
        total_files = len(self.scanned_files)
        files_with_nfo = sum(1 for f in self.scanned_files if f['nfo_path'])
        files_with_images = sum(1 for f in self.scanned_files if f['images'])
        
        image_types_found = set()
        for file_info in self.scanned_files:
            image_types_found.update(file_info['images'].keys())
        
        return {
            'total_files': total_files,
            'files_with_nfo': files_with_nfo,
            'files_with_images': files_with_images,
            'image_types_found': image_types_found,
            'nfo_coverage': f"{files_with_nfo}/{total_files}" if total_files > 0 else "0/0"
        }
