"""
系列管理 API 路由
提供系列的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.management_service import SeriesService
from app.schemas.schemas import (
    SeriesCreate, SeriesUpdate, SeriesResponse, SeriesListResponse, BaseResponse,
    BatchDeleteRequest, BatchDeleteResponse, UnifiedDeleteRequest
)
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/series",
    tags=["系列管理"],
    responses={404: {"description": "系列不存在"}}
)


@router.post("", response_model=BaseResponse)
async def create_series(series_data: SeriesCreate, db: Session = Depends(get_db)):
    """
    创建新系列
    
    Args:
        series_data: 系列创建数据
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        series_service = SeriesService(db)
        series = series_service.create_series(series_data)
        
        if not series:
            raise HTTPException(status_code=400, detail="系列名称已存在")
        
        return BaseResponse(
            success=True,
            message="系列创建成功",
            data=SeriesResponse.model_validate(series)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建系列失败: {str(e)}")


@router.get("", response_model=SeriesListResponse)
async def get_series(
    limit: int = 50,
    offset: int = 0,
    search: str = None,
    db: Session = Depends(get_db)
):
    """
    获取系列列表
    
    Args:
        limit: 每页数量 (1-100)
        offset: 偏移量
        search: 搜索关键词
        db: 数据库会话
        
    Returns:
        系列列表
    """
    try:
        # 参数验证
        if limit < 1 or limit > 1000:
            raise HTTPException(status_code=400, detail="limit 必须在 1-1000 之间")
        if offset < 0:
            raise HTTPException(status_code=400, detail="offset 必须大于等于 0")
        
        series_service = SeriesService(db)
        series_list, total_count = series_service.get_series_list(limit=limit, offset=offset, search=search)
        
        # 为每个系列添加电影数量
        series_responses = []
        for series in series_list:
            series_info = series_service.get_series_with_movie_count(series.id)
            series_response = SeriesResponse.model_validate(series)
            series_response.movie_count = series_info["movie_count"] if series_info else 0
            series_responses.append(series_response)
        
        return SeriesListResponse(
            success=True,
            message=f"获取到 {len(series_list)} 个系列",
            data=series_responses,
            total_count=total_count,
            limit=limit,
            offset=offset
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系列列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取系列列表失败: {str(e)}")


@router.get("/{series_id}", response_model=BaseResponse)
async def get_series_detail(series_id: int, db: Session = Depends(get_db)):
    """
    获取指定系列详情
    
    Args:
        series_id: 系列 ID
        db: 数据库会话
        
    Returns:
        系列详情
    """
    try:
        series_service = SeriesService(db)
        series_info = series_service.get_series_with_movie_count(series_id)
        
        if not series_info:
            raise HTTPException(status_code=404, detail="系列不存在")
        
        series_response = SeriesResponse.model_validate(series_info["series"])
        series_response.movie_count = series_info["movie_count"]
        
        return BaseResponse(
            success=True,
            message="获取系列详情成功",
            data=series_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系列详情时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取系列详情失败: {str(e)}")


@router.put("/{series_id}", response_model=BaseResponse)
async def update_series(series_id: int, series_data: SeriesUpdate, db: Session = Depends(get_db)):
    """
    更新系列信息
    
    Args:
        series_id: 系列 ID
        series_data: 更新数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        series_service = SeriesService(db)
        series = series_service.update_series(series_id, series_data)
        
        if not series:
            # 检查是否是因为系列不存在
            existing_series = series_service.get_series_by_id(series_id)
            if not existing_series:
                raise HTTPException(status_code=404, detail="系列不存在")
            else:
                raise HTTPException(status_code=400, detail="系列名称已存在")
        
        return BaseResponse(
            success=True,
            message="系列更新成功",
            data=SeriesResponse.model_validate(series)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新系列失败: {str(e)}")


@router.delete("", response_model=BaseResponse)
async def delete_series(request: UnifiedDeleteRequest, db: Session = Depends(get_db)):
    """
    统一删除系列（支持单项和批量删除）

    Args:
        request: 统一删除请求，支持单个ID或ID列表
        db: 数据库会话

    Returns:
        删除结果
    """
    try:
        series_service = SeriesService(db)
        result = series_service.unified_delete_series(request)

        if isinstance(result, tuple):
            # 单项删除结果
            success, message = result
            if not success:
                if "不存在" in message:
                    raise HTTPException(status_code=404, detail=message)
                else:
                    raise HTTPException(status_code=400, detail=message)

            return BaseResponse(
                success=True,
                message=message
            )
        else:
            # 批量删除结果
            batch_result = result
            if batch_result.failed_count == 0:
                message = f"成功删除 {batch_result.success_count} 个系列"
            elif batch_result.success_count == 0:
                message = f"删除失败，{batch_result.failed_count} 个系列无法删除"
            else:
                message = f"成功删除 {batch_result.success_count} 个系列，{batch_result.failed_count} 个失败"

            return BaseResponse(
                success=True,
                message=message,
                data=batch_result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除系列失败: {str(e)}")
