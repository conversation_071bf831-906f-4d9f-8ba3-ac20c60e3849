{% extends "base.html" %}

{% block title %}演员管理 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-people text-primary" style="font-size: 2rem;" aria-label="演员管理图标"></i>
                    演员管理
                </h1>
                <p class="text-base-content/70 mt-2">管理影片演员，组织您的媒体库</p>
            </div>
            <div class="flex items-center gap-3">
                <button type="button" class="btn btn-primary btn-sm" id="add-actors-btn">
                    <i class="bi bi-plus" aria-label="添加图标"></i>
                    添加演员
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 搜索和操作区域 -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-6">
    <div class="card-body">
        <div class="flex flex-col lg:flex-row gap-4 items-end">
            <!-- 搜索输入框 -->
            <div class="form-control flex-1">
                <label class="label">
                    <span class="label-text font-medium">搜索演员</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                    </div>
                    <input type="text" class="input input-bordered w-full pl-10" id="search-input" placeholder="搜索演员名称或描述...">
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-control">
                <label class="label lg:opacity-0">
                    <span class="label-text">　</span>
                </label>
                <div class="flex gap-2">
                    <button type="button" class="btn btn-outline" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise mr-2" aria-label="刷新图标"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-error hidden" id="batch-delete-btn">
                        <i class="bi bi-trash mr-2" aria-label="批量删除图标"></i>
                        批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="stats shadow mb-6 w-full">
    <div class="stat">
        <div class="stat-figure text-primary">
            <i class="bi bi-people text-primary" style="font-size: 2rem;" aria-label="总演员图标"></i>
        </div>
        <div class="stat-title">总演员数</div>
        <div class="stat-value text-primary" id="total-actors">0</div>
        <div class="stat-desc">系统中的演员总数</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-secondary">
            <i class="bi bi-check-circle text-secondary" style="font-size: 2rem;" aria-label="已使用演员图标"></i>
        </div>
        <div class="stat-title">已使用演员</div>
        <div class="stat-value text-secondary" id="used-actors">0</div>
        <div class="stat-desc">已关联影片的演员</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-accent">
            <i class="bi bi-circle text-accent" style="font-size: 2rem;" aria-label="未使用演员图标"></i>
        </div>
        <div class="stat-title">未使用演员</div>
        <div class="stat-value text-accent" id="unused-actors">0</div>
        <div class="stat-desc">未关联影片的演员</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-info">
            <i class="bi bi-info-circle text-info" style="font-size: 2rem;" aria-label="选中演员图标"></i>
        </div>
        <div class="stat-title">选中演员</div>
        <div class="stat-value text-info" id="selected-actors">0</div>
        <div class="stat-desc">当前选中的演员数</div>
    </div>
</div>

<!-- 演员表格 -->
<div id="table-view" class="card bg-base-100 shadow-lg border border-base-300">
    <div class="card-body p-0">
        <!-- 表格头部 -->
        <div class="flex items-center justify-between p-6 border-b border-base-300">
            <h3 class="text-xl font-semibold">演员列表</h3>
        </div>

        <!-- 表格内容 -->
        <div class="overflow-x-auto">
            <table class="table table-zebra">
                <thead>
                    <tr>
                        <th class="w-12">
                            <input type="checkbox" class="checkbox checkbox-primary" id="table-select-all">
                        </th>
                        <th>演员姓名</th>
                        <th>传记</th>
                        <th>影片数量</th>
                        <th>创建时间</th>
                        <th class="w-32">操作</th>
                    </tr>
                </thead>
                <tbody id="actors-table-body">
                    <!-- 演员数据将在这里动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 表格底部 -->
        <div class="flex items-center justify-between p-6 border-t border-base-300">
            <div class="text-sm text-base-content/60" id="actors-count-info">
                共 0 个演员
            </div>
            <div id="pagination-container">
                <!-- 分页将在这里生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="loading-state">
    <span class="loading loading-spinner loading-lg mb-4"></span>
    <p class="text-base-content/60">加载演员数据...</p>
</div>

<!-- 空状态 -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="empty-state">
    <div class="bg-base-200 rounded-full p-6 mb-6">
        <i class="bi bi-people text-base-content/40" style="font-size: 4rem;" aria-label="空演员图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">暂无演员</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md">您还没有创建任何演员，点击下方按钮创建第一个演员。</p>
    <button type="button" class="btn btn-primary btn-wide" id="add-first-actors-btn">
        <i class="bi bi-plus mr-2" aria-label="添加图标"></i>
        创建第一个演员
    </button>
</div>

<!-- 添加/编辑演员模态框 -->
<dialog id="actors-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-6" id="modal-title">添加演员</h3>

        <form id="actors-form" class="space-y-6">
            <input type="hidden" id="actors-id">

            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">演员姓名 <span class="text-error">*</span></span>
                </label>
                <input type="text" class="input input-bordered w-full" id="actors-name" name="name" placeholder="请输入演员姓名" required>
                <div class="label">
                    <span class="label-text-alt text-error hidden" id="actors-name-error"></span>
                </div>
            </div>
            <!-- 传记 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">传记</span>
                </label>
                <textarea class="textarea textarea-bordered w-full" id="actors-biography" placeholder="请输入演员传记信息（可选）"></textarea>
                <div class="label">
                    <span class="label-text-alt">最多 500 字符</span>
                </div>
            </div>
            <!-- 按钮组 -->
            <div class="modal-action">
                <button type="button" class="btn btn-ghost" onclick="document.getElementById('actors-modal').close()">取消</button>
                <button type="submit" class="btn btn-primary" id="actors-submit-btn">
                    <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                    <i class="bi bi-check mr-2" aria-label="保存图标"></i>
                    保存
                </button>
            </div>
        </form>
    </div>
</dialog>

<!-- 删除确认模态框 -->
<dialog id="delete-actors-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">确认删除演员</h3>
        <p class="mb-4" id="delete-actors-message">您确定要删除这个演员吗？</p>

        <div class="form-control mb-6">
            <label class="label cursor-pointer justify-start gap-3">
                <input type="checkbox" class="checkbox checkbox-warning" id="force-delete-actors-checkbox">
                <span class="label-text">强制删除（忽略关联关系）</span>
            </label>
            <div class="label">
                <span class="label-text-alt">勾选此项将强制删除演员，即使它被影片使用</span>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('delete-actors-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-delete-actors-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                删除
            </button>
        </div>
    </div>
</dialog>

<!-- 批量删除确认模态框 -->
<dialog id="batch-delete-actors-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">批量删除确认</h3>
        <p class="mb-4" id="batch-delete-actors-message">您确定要删除选中的演员吗？</p>

        <div class="form-control mb-6">
            <label class="label cursor-pointer justify-start gap-3">
                <input type="checkbox" class="checkbox checkbox-warning" id="force-batch-delete-actors-checkbox">
                <span class="label-text">强制删除（忽略关联关系）</span>
            </label>
            <div class="label">
                <span class="label-text-alt">勾选此项将强制删除演员，即使它们被影片使用</span>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('batch-delete-actors-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-batch-delete-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                删除全部
            </button>
        </div>
    </div>
</dialog>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/data-manager.js?=v0.9.4') }}"></script>
{% endblock %}
