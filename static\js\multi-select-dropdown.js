/**
 * 搜索下拉多选框组件
 * 支持搜索、多选、标签显示等功能
 */

class MultiSelectDropdown {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            placeholder: '请选择...',
            searchPlaceholder: '搜索...',
            noDataText: '暂无数据',
            noResultsText: '未找到匹配项',
            maxDisplayTags: 3,
            allowClear: true,
            allowCreate: false,
            createText: '按回车创建',
            createApiUrl: null,
            createApiMethod: 'POST',
            onCreateSuccess: null,
            onCreateError: null,
            ...options
        };
        
        this.data = [];
        this.selectedItems = new Map();
        this.isOpen = false;
        this.searchTerm = '';
        
        this.init();
    }
    
    init() {
        this.render();
        this.bindEvents();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="multi-select-dropdown relative overflow-visible">
                <!-- 选择框主体 -->
                <div class="multi-select-trigger input input-bordered w-full min-h-12 h-auto cursor-pointer flex items-center justify-between p-2">
                    <div class="multi-select-content flex-1 flex flex-wrap gap-1 min-h-8">
                        <span class="multi-select-placeholder text-base-content/50">${this.options.placeholder}</span>
                    </div>
                    <div class="multi-select-arrow ml-2 flex-shrink-0">
                        <i class="bi bi-chevron-down transition-transform duration-200" aria-label="下拉箭头图标"></i>
                    </div>
                </div>
                
                <!-- 下拉面板 -->
                <div class="multi-select-dropdown-panel absolute top-full left-0 right-0 z-[9999] bg-base-100 border border-base-300 rounded-lg shadow-lg mt-1 hidden max-h-64 sm:max-h-80 md:max-h-96 overflow-visible">
                    <!-- 搜索框 -->
                    <div class="p-3 sm:p-2 border-b border-base-300">
                        <input type="text" class="multi-select-search input input-sm input-bordered w-full text-sm" placeholder="${this.options.searchPlaceholder}">
                    </div>
                    
                    <!-- 选项列表 -->
                    <div class="multi-select-options overflow-y-auto max-h-48 sm:max-h-60 md:max-h-72">
                        <div class="multi-select-loading p-4 text-center text-base-content/50">
                            <span class="loading loading-spinner loading-sm mr-2"></span>
                            加载中...
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.cacheElements();
    }
    
    cacheElements() {
        this.trigger = this.container.querySelector('.multi-select-trigger');
        this.content = this.container.querySelector('.multi-select-content');
        this.placeholder = this.container.querySelector('.multi-select-placeholder');
        this.arrow = this.container.querySelector('.multi-select-arrow i');
        this.panel = this.container.querySelector('.multi-select-dropdown-panel');
        this.searchInput = this.container.querySelector('.multi-select-search');
        this.optionsContainer = this.container.querySelector('.multi-select-options');
    }
    
    bindEvents() {
        // 点击触发器
        this.trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggle();
        });
        
        // 搜索输入
        this.searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.renderOptions();
        });

        // 回车键创建新选项
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.handleCreateNewItem();
            }
        });
        
        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });
        
        // 阻止面板点击事件冒泡
        this.panel.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 窗口大小改变时重新调整定位
        window.addEventListener('resize', () => {
            if (this.isOpen) {
                this.adjustPanelPosition();
            }
        });
    }
    
    setData(data) {
        this.data = data || [];
        this.renderOptions();
    }
    
    renderOptions() {
        if (this.data.length === 0) {
            this.optionsContainer.innerHTML = `
                <div class="p-4 text-center text-base-content/50">
                    ${this.options.noDataText}
                </div>
            `;
            return;
        }
        
        const filteredData = this.data.filter(item => 
            item.name.toLowerCase().includes(this.searchTerm) ||
            (item.role && item.role.toLowerCase().includes(this.searchTerm))
        );
        
        if (filteredData.length === 0) {
            // 检查是否允许创建新选项
            if (this.options.allowCreate && this.searchTerm.trim()) {
                this.optionsContainer.innerHTML = `
                    <div class="p-4 text-center">
                        <div class="text-base-content/50 mb-2">${this.options.noResultsText}</div>
                        <div class="text-primary text-sm">
                            <kbd class="kbd kbd-sm">Enter</kbd> ${this.options.createText} "${this.searchTerm.trim()}"
                        </div>
                    </div>
                `;
            } else {
                this.optionsContainer.innerHTML = `
                    <div class="p-4 text-center text-base-content/50">
                        ${this.options.noResultsText}
                    </div>
                `;
            }
            return;
        }
        
        const html = filteredData.map(item => {
            // 使用更严格的选中状态检查，支持字符串和数字 ID
            const isSelected = this.selectedItems.has(item.id.toString()) ||
                              this.selectedItems.has(item.id) ||
                              Array.from(this.selectedItems.keys()).some(key =>
                                  key == item.id.toString() || key == item.id
                              );
            const displayText = item.role ? `${item.name} (${item.role})` : item.name;

            return `
                <div class="multi-select-option flex items-center p-3 sm:p-2 hover:bg-base-200 cursor-pointer ${isSelected ? 'bg-primary/10' : ''} min-h-12 sm:min-h-10"
                     data-value="${item.id}" data-text="${item.name}" data-role="${item.role || ''}">
                    <input type="checkbox" class="checkbox checkbox-sm mr-3" ${isSelected ? 'checked' : ''}>
                    <span class="flex-1 text-sm sm:text-base">${displayText}</span>
                </div>
            `;
        }).join('');
        
        this.optionsContainer.innerHTML = html;
        
        // 绑定选项点击事件
        this.optionsContainer.querySelectorAll('.multi-select-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const value = option.dataset.value;
                const text = option.dataset.text;
                const role = option.dataset.role;
                
                if (this.selectedItems.has(value)) {
                    this.removeItem(value);
                } else {
                    this.addItem({ id: value, name: text, role: role });
                }
                
                this.renderSelectedTags();
                this.renderOptions(); // 重新渲染以更新选中状态
            });
        });
    }
    
    addItem(item) {
        // 确保使用字符串作为 key，保持一致性
        this.selectedItems.set(item.id.toString(), item);
        this.renderSelectedTags();
        this.triggerChange();
    }

    removeItem(id) {
        // 尝试删除字符串和数字形式的 ID
        this.selectedItems.delete(id.toString());
        this.selectedItems.delete(id);
        this.renderSelectedTags();
        this.triggerChange();
    }
    
    renderSelectedTags() {
        const selectedArray = Array.from(this.selectedItems.values());
        
        if (selectedArray.length === 0) {
            this.content.innerHTML = `<span class="multi-select-placeholder text-base-content/50">${this.options.placeholder}</span>`;
            return;
        }
        
        const maxDisplay = this.options.maxDisplayTags;
        const displayItems = selectedArray.slice(0, maxDisplay);
        const remainingCount = selectedArray.length - maxDisplay;
        
        let html = displayItems.map(item => {
            const displayText = item.role ? `${item.name} (${item.role})` : item.name;
            return `
                <span class="multi-select-tag badge badge-primary gap-1 text-xs">
                    ${displayText}
                    <button type="button" class="multi-select-tag-remove btn btn-ghost btn-xs p-0 min-h-0 h-4 w-4" data-id="${item.id}">
                        <i class="bi bi-x" style="font-size: 0.75rem;" aria-label="移除标签图标"></i>
                    </button>
                </span>
            `;
        }).join('');
        
        if (remainingCount > 0) {
            html += `<span class="badge badge-outline text-xs">+${remainingCount}</span>`;
        }
        
        this.content.innerHTML = html;
        
        // 绑定删除按钮事件
        this.content.querySelectorAll('.multi-select-tag-remove').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = btn.dataset.id;
                this.removeItem(id);
            });
        });
    }
    
    open() {
        this.isOpen = true;
        this.panel.classList.remove('hidden');
        this.arrow.style.transform = 'rotate(180deg)';

        // 动态调整下拉面板的定位，确保在模态框中也能正确显示
        this.adjustPanelPosition();

        this.searchInput.focus();
        this.searchInput.value = '';
        this.searchTerm = '';
        this.renderOptions();
    }

    /**
     * 调整下拉面板的位置
     */
    adjustPanelPosition() {
        const rect = this.trigger.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const isMobile = window.innerWidth < 640; // sm breakpoint
        const panelHeight = isMobile ? 256 : 320; // 根据屏幕大小调整估算高度

        // 检查是否有足够空间在下方显示
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;

        if (spaceBelow < panelHeight && spaceAbove > spaceBelow) {
            // 如果下方空间不足且上方空间更大，则在上方显示
            this.panel.classList.remove('top-full', 'mt-1');
            this.panel.classList.add('bottom-full', 'mb-1');
        } else {
            // 默认在下方显示
            this.panel.classList.remove('bottom-full', 'mb-1');
            this.panel.classList.add('top-full', 'mt-1');
        }
    }
    
    close() {
        this.isOpen = false;
        this.panel.classList.add('hidden');
        this.arrow.style.transform = 'rotate(0deg)';

        // 重置定位类
        this.panel.classList.remove('bottom-full', 'mb-1');
        this.panel.classList.add('top-full', 'mt-1');
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    getSelectedValues() {
        return Array.from(this.selectedItems.keys()).map(id => parseInt(id));
    }
    
    setSelectedValues(values) {
        this.selectedItems.clear();

        if (values && values.length > 0) {
            values.forEach(value => {
                let item = null;

                // 如果 value 是对象，直接使用
                if (typeof value === 'object' && value.id) {
                    item = this.data.find(d =>
                        d.id == value.id ||
                        d.id.toString() === value.id.toString()
                    );
                    // 如果在数据中找不到，使用传入的对象
                    if (!item) {
                        item = value;
                    }
                } else {
                    // 如果 value 是 ID，查找对应的数据项
                    item = this.data.find(d =>
                        d.id == value ||
                        d.id.toString() === value.toString()
                    );
                }

                if (item) {
                    // 确保使用字符串作为 key，保持一致性
                    this.selectedItems.set(item.id.toString(), item);
                }
            });
        }

        this.renderSelectedTags();
        this.renderOptions();
    }
    
    clear() {
        this.selectedItems.clear();
        this.renderSelectedTags();
        this.triggerChange();
    }
    
    triggerChange() {
        const event = new CustomEvent('change', {
            detail: {
                selectedItems: Array.from(this.selectedItems.values()),
                selectedValues: this.getSelectedValues()
            }
        });
        this.container.dispatchEvent(event);
    }

    /**
     * 处理创建新选项
     */
    async handleCreateNewItem() {
        if (!this.options.allowCreate || !this.searchTerm.trim()) {
            return;
        }

        const searchText = this.searchTerm.trim();

        // 检查是否已存在相同名称的选项
        const existingItem = this.data.find(item =>
            item.name.toLowerCase() === searchText.toLowerCase()
        );

        if (existingItem) {
            // 如果已存在，直接选择该项
            this.addItem(existingItem);
            this.searchInput.value = '';
            this.searchTerm = '';
            this.renderOptions();
            return;
        }

        try {
            // 显示创建中状态
            this.optionsContainer.innerHTML = `
                <div class="p-4 text-center">
                    <span class="loading loading-spinner loading-sm mr-2"></span>
                    正在创建 "${searchText}"...
                </div>
            `;

            // 调用API创建新选项
            const newItem = await this.createNewItem(searchText);

            if (newItem) {
                // 添加到数据列表
                this.data.push(newItem);

                // 自动选择新创建的项
                this.addItem(newItem);

                // 清空搜索框
                this.searchInput.value = '';
                this.searchTerm = '';

                // 重新渲染选项
                this.renderOptions();

                // 触发成功回调
                if (this.options.onCreateSuccess) {
                    this.options.onCreateSuccess(newItem);
                }

                // 显示成功提示
                this.showCreateSuccess(searchText);
            }

        } catch (error) {
            console.error('创建新选项失败:', error);

            // 显示错误状态
            this.optionsContainer.innerHTML = `
                <div class="p-4 text-center">
                    <div class="text-error text-sm">创建失败: ${error.message}</div>
                    <div class="text-base-content/50 text-xs mt-1">请稍后重试</div>
                </div>
            `;

            // 触发错误回调
            if (this.options.onCreateError) {
                this.options.onCreateError(error);
            }

            // 3秒后恢复搜索状态
            setTimeout(() => {
                this.renderOptions();
            }, 3000);
        }
    }

    /**
     * 调用API创建新选项
     */
    async createNewItem(name) {
        if (!this.options.createApiUrl) {
            throw new Error('未配置创建API地址');
        }

        const requestData = { name: name };

        // 如果是演员类型，可能需要处理角色信息
        if (this.options.type === 'actors' && name.includes('(') && name.includes(')')) {
            const match = name.match(/^(.+?)\s*\((.+?)\)$/);
            if (match) {
                requestData.name = match[1].trim();
                requestData.role = match[2].trim();
            }
        }

        const response = await fetch(this.options.createApiUrl, {
            method: this.options.createApiMethod,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || '创建失败');
        }

        return result.data;
    }

    /**
     * 显示创建成功提示
     */
    showCreateSuccess(name) {
        // 可以在这里添加toast提示或其他成功反馈
        if (window.toast) {
            window.toast.success(`成功创建 "${name}"`);
        }
    }

    destroy() {
        this.container.innerHTML = '';
    }
}
