"""add_mapping_rules_table

Revision ID: 73c1d9df3f6b
Revises: c371a52160b1
Create Date: 2025-07-30 19:46:11.972555

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '73c1d9df3f6b'
down_revision: Union[str, Sequence[str], None] = 'c371a52160b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 使用批量模式为 directories 表添加唯一约束
    with op.batch_alter_table('directories', schema=None) as batch_op:
        batch_op.create_unique_constraint('uq_directories_name', ['name'])

    # 创建映射规则表
    op.create_table('mapping_rules',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('type', sa.String(length=50), nullable=False),
        sa.Column('original_value', sa.String(length=255), nullable=False),
        sa.Column('mapped_value', sa.String(length=255), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('type', 'original_value', name='uq_mapping_type_original')
    )
    op.create_index(op.f('ix_mapping_rules_original_value'), 'mapping_rules', ['original_value'], unique=False)
    op.create_index(op.f('ix_mapping_rules_status'), 'mapping_rules', ['status'], unique=False)
    op.create_index(op.f('ix_mapping_rules_type'), 'mapping_rules', ['type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 删除映射规则表
    op.drop_index(op.f('ix_mapping_rules_type'), table_name='mapping_rules')
    op.drop_index(op.f('ix_mapping_rules_status'), table_name='mapping_rules')
    op.drop_index(op.f('ix_mapping_rules_original_value'), table_name='mapping_rules')
    op.drop_table('mapping_rules')

    # 使用批量模式删除 directories 表的唯一约束
    with op.batch_alter_table('directories', schema=None) as batch_op:
        batch_op.drop_constraint('uq_directories_name', type_='unique')
    # ### end Alembic commands ###
