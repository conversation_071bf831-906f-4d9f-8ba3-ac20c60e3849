{% extends "base.html" %}

{% block title %}收藏夹 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-heart-fill text-primary" style="font-size: 2rem;" aria-label="收藏夹图标"></i>
                    收藏夹
                </h1>
                <p class="text-base-content/70 mt-2">管理您收藏的影片</p>
            </div>
            <div class="flex items-center gap-3">
                <button type="button" class="btn btn-outline btn-sm" id="batch-select-btn" title="批量选择">
                    <i class="bi bi-check-square" aria-label="批量选择图标"></i>
                    批量选择
                </button>
                <div class="dropdown dropdown-right sm:dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-outline btn-sm">
                        <i class="bi bi-three-dots-vertical" aria-label="更多操作图标"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                        <li><a href="#" id="refresh-btn"><i class="bi bi-arrow-clockwise" aria-label="刷新图标"></i> 刷新列表</a></li>
                        <li><a href="#" id="clear-search-btn"><i class="bi bi-x-circle" aria-label="清除搜索图标"></i> 清除搜索</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作工具栏将由 batch-toolbar.js 动态创建 -->
{% endblock %}

{% block content %}
<!-- Search and Filters -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-4">
    <div class="card-body p-4">
        <!-- 主搜索区域 -->
        <div class="flex flex-col lg:flex-row gap-3 items-end mb-4">
            <!-- 搜索输入框 -->
            <div class="form-control flex-1">
                <label class="label py-1">
                    <span class="label-text font-medium text-sm">搜索收藏</span>
                    <span class="label-text-alt text-xs text-base-content/60">支持标题、演员、导演搜索</span>
                </label>
                <div class="join w-full">
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                        </div>
                        <input type="text"
                               class="input input-bordered w-full pl-9 pr-10 join-item focus:input-primary"
                               id="search-input"
                               placeholder="搜索收藏的影片..."
                               autocomplete="off">
                        <button type="button"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-base-content/40 hover:text-base-content hidden"
                                id="clear-search-input-btn"
                                title="清除搜索">
                            <i class="bi bi-x-circle-fill" aria-label="清除搜索图标"></i>
                        </button>
                    </div>
                    <button type="button" class="btn btn-primary join-item" id="search-btn">
                        <i class="bi bi-search" aria-label="搜索按钮图标"></i>
                    </button>
                </div>
            </div>

            <!-- 排序选择 -->
            <div class="form-control w-full lg:w-48">
                <label class="label py-1">
                    <span class="label-text font-medium text-sm">排序方式</span>
                </label>
                <select class="select select-bordered focus:select-primary" id="sort-select">
                    <option value="created_at-desc">收藏时间 ↓</option>
                    <option value="created_at-asc">收藏时间 ↑</option>
                    <option value="title-asc">标题 A-Z</option>
                    <option value="title-desc">标题 Z-A</option>
                    <option value="year-desc">年份 ↓</option>
                    <option value="year-asc">年份 ↑</option>
                    <option value="rating-desc">评分 ↓</option>
                    <option value="rating-asc">评分 ↑</option>
                </select>
            </div>


        </div>


    </div>
</div>

<!-- Movies Grid -->
<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 md:gap-6" id="favorites-container">
    <!-- Favorites will be loaded here -->
</div>

<!-- Loading Skeleton -->
<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-4 md:gap-6 hidden" id="loading-skeleton">
    <!-- Skeleton cards will be generated here -->
</div>

<!-- Empty State -->
<div class="text-center py-16 hidden" id="empty-state">
    <div class="max-w-md mx-auto">
        <i class="bi bi-heart text-6xl text-base-content/30 mb-4" aria-label="空状态图标"></i>
        <h3 class="text-xl font-semibold text-base-content mb-2">暂无收藏</h3>
        <p class="text-base-content/60 mb-6">您还没有收藏任何影片，快去影片库添加一些收藏吧！</p>
        <a href="/movies" class="btn btn-primary">
            <i class="bi bi-film mr-2" aria-label="影片库图标"></i>
            浏览影片库
        </a>
    </div>
</div>

<!-- Error State -->
<div class="text-center py-16 hidden" id="error-state">
    <div class="max-w-md mx-auto">
        <i class="bi bi-exclamation-triangle text-6xl text-error mb-4" aria-label="错误图标"></i>
        <h3 class="text-xl font-semibold text-base-content mb-2">加载失败</h3>
        <p class="text-base-content/60 mb-6" id="error-message">加载收藏列表时出现错误，请稍后重试。</p>
        <button type="button" class="btn btn-primary" id="error-retry-btn">
            <i class="bi bi-arrow-clockwise mr-2" aria-label="重试图标"></i>
            重试
        </button>
    </div>
</div>

<!-- Pagination -->
<div class="flex flex-col md:flex-row justify-between items-center mt-8 gap-4" id="pagination-container">
    <div class="text-base-content/70" id="pagination-info">
        显示 0 - 0 条，共 0 条记录
    </div>
    <div class="join" id="pagination-controls">
        <!-- Pagination controls will be generated here -->
    </div>
</div>

<!-- Load More Button (for infinite scroll alternative) -->
<div class="flex justify-center mt-8 hidden" id="load-more-container">
    <button type="button" class="btn btn-outline btn-primary btn-wide" id="load-more-btn">
        <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
        <i class="bi bi-plus mr-2" aria-label="加载更多图标"></i>
        加载更多
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/multi-select-dropdown.js?=v0.9.4') }}"></script>
<script src="{{ url_for('static', path='/js/favorite-manager.js?=v0.9.4') }}"></script>
<script src="{{ url_for('static', path='/js/batch-toolbar.js?=v0.9.4') }}"></script>
<script src="{{ url_for('static', path='/js/favorites.js?=v0.9.4') }}"></script>
{% endblock %}
