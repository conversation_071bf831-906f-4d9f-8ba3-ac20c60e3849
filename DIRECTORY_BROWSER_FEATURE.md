# 目录浏览器功能说明

## 功能概述
为目录管理功能添加了路径选择器组件，用户可以通过图形界面浏览服务器文件系统并选择目录路径，而不需要手动输入完整路径。

## 新增功能

### 1. 后端API支持
- **新增端点**: `GET /api/directories/browse`
- **功能**: 浏览服务器端目录结构
- **参数**: `path` (可选) - 要浏览的目录路径
- **返回**: 目录列表、面包屑导航、当前路径信息

### 2. 前端组件
- **目录浏览器模态框**: 显示服务器端目录结构
- **路径选择器**: 在目录路径输入框旁添加"浏览"按钮
- **面包屑导航**: 显示当前路径位置
- **目录树**: 支持展开/折叠，双击进入下级目录

### 3. 用户交互
- **浏览按钮**: 点击打开目录浏览器
- **目录导航**: 双击目录进入下一级
- **路径选择**: 点击"选择"按钮选中目录
- **确认选择**: 将选中路径填入主表单

## 技术实现

### 后端实现 (`app/api/directories.py`)

#### 安全特性
1. **路径安全检查**: 限制可访问的目录范围
2. **跨平台支持**: 
   - Windows: 支持驱动器浏览 (C:\, D:\, 等)
   - Linux/macOS: 支持常用目录 (/home, /media, /mnt, 等)
3. **权限验证**: 防止访问系统敏感目录

#### API端点详情
```python
@router.get("/browse")
async def browse_directories(path: str = Query(None)):
    # 返回目录列表和导航信息
```

**响应格式**:
```json
{
    "success": true,
    "data": {
        "current_path": "/path/to/current",
        "parent_path": "/path/to/parent",
        "items": [
            {
                "name": "子目录名",
                "path": "/完整/路径",
                "is_directory": true,
                "modified_time": "时间戳"
            }
        ],
        "breadcrumbs": [
            {
                "name": "显示名称",
                "path": "/路径",
                "is_current": false
            }
        ],
        "total_count": 5
    }
}
```

### 前端实现

#### 模板修改 (`templates/directories_manager.html`)
1. **路径输入框增强**: 添加"浏览"按钮
2. **目录浏览模态框**: 完整的目录浏览界面
3. **响应式设计**: 支持不同屏幕尺寸

#### JavaScript组件 (`static/js/directory-browser.js`)
1. **DirectoryBrowser类**: 封装所有目录浏览功能
2. **事件处理**: 目录导航、路径选择、确认操作
3. **状态管理**: 加载状态、错误处理、路径跟踪

#### 主要方法
- `openBrowser()`: 打开目录浏览器
- `loadDirectories(path)`: 加载指定路径的目录列表
- `navigateToDirectory(path)`: 导航到指定目录
- `selectDirectory(path)`: 选择目录路径
- `confirmSelection()`: 确认选择并填入主表单

## 安全考虑

### 1. 路径限制
- **Windows**: 仅允许访问用户目录和数据驱动器
- **Linux/macOS**: 仅允许访问 /home, /media, /mnt 等安全目录

### 2. 输入验证
- 路径规范化处理
- 防止目录遍历攻击
- 权限检查

### 3. 错误处理
- 优雅的错误提示
- 权限不足时的友好提示
- 网络错误的重试机制

## 用户体验优化

### 1. 界面设计
- 使用DaisyUI v5组件保持风格一致
- 响应式布局适配不同设备
- 清晰的视觉层次和交互反馈

### 2. 操作便利性
- 面包屑导航快速跳转
- 双击进入目录
- 一键选择和确认
- 实时路径预览

### 3. 性能优化
- 按需加载目录内容
- 加载状态指示
- 错误重试机制

## 使用方法

### 1. 添加目录时
1. 点击"添加目录"按钮
2. 在目录路径字段旁点击"浏览"按钮
3. 在弹出的目录浏览器中导航到目标目录
4. 点击目录旁的"选择"按钮
5. 点击"确认选择"将路径填入表单
6. 填写目录名称并保存

### 2. 目录导航
- **进入子目录**: 双击目录名
- **返回上级**: 点击".."项或使用面包屑导航
- **快速跳转**: 点击面包屑中的任意路径

### 3. 路径选择
- **选择当前目录**: 点击目录旁的"选择"按钮
- **预览路径**: 在"当前选中路径"字段查看
- **确认选择**: 点击"确认选择"按钮

## 兼容性

### 操作系统支持
- ✅ Windows 10/11
- ✅ Linux (Ubuntu, CentOS, 等)
- ✅ macOS

### 浏览器支持
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## 注意事项

1. **权限要求**: 服务器进程需要有读取目标目录的权限
2. **路径格式**: 自动处理不同操作系统的路径分隔符
3. **性能考虑**: 大型目录可能需要较长加载时间
4. **安全限制**: 某些系统目录可能无法访问

## 后续优化建议

1. **搜索功能**: 在目录浏览器中添加搜索框
2. **收藏夹**: 保存常用目录路径
3. **批量选择**: 支持选择多个目录
4. **预览功能**: 显示目录大小和文件数量
5. **拖拽支持**: 支持拖拽选择目录
