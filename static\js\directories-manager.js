/**
 * 目录管理器
 * 使用DaisyUI v5组件重新构建的目录管理功能
 */
class DirectoriesManager {
    constructor() {
        this.directories = [];
        this.selectedDirectories = new Set();
        this.currentDirectory = null;
        this.searchTimeout = null;
        this.init();
    }

    /**
     * 初始化目录管理器
     */
    init() {
        // 检查是否在正确的页面上
        if (!this.isOnDirectoriesPage()) {
            return;
        }

        this.bindEvents();
        this.loadDirectories();
    }

    /**
     * 检查是否在目录管理页面
     */
    isOnDirectoriesPage() {
        return document.getElementById('directories-container') !== null;
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 添加目录按钮
        document.getElementById('add-directory-btn')?.addEventListener('click', () => {
            this.showDirectoryModal();
        });

        document.getElementById('empty-add-directory-btn')?.addEventListener('click', () => {
            this.showDirectoryModal();
        });

        // 表单提交
        document.getElementById('directory-form')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDirectory();
        });

        // 搜索
        document.getElementById('search-input')?.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // 刷新按钮
        document.getElementById('refresh-directories-btn')?.addEventListener('click', () => {
            this.loadDirectories();
        });

        // 批量扫描
        document.getElementById('batch-scan-btn')?.addEventListener('click', () => {
            this.handleBatchScan();
        });

        // 删除确认
        document.getElementById('confirm-delete-directory-btn')?.addEventListener('click', () => {
            this.confirmDelete();
        });


    }

    /**
     * 加载目录列表
     */
    async loadDirectories() {
        try {
            this.showLoading();

            const response = await fetch('/api/directories');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                this.directories = result.data || [];

                this.renderDirectories();
                this.hideLoading();
            } else {
                throw new Error(result.message || '加载目录失败');
            }
        } catch (error) {
            console.error('Failed to load directories:', error);

            handleApiError(error, '加载目录列表');
            this.showError();
        }
    }

    /**
     * 渲染目录列表
     */
    renderDirectories(filteredDirectories = null) {
        const directories = filteredDirectories || this.directories;
        const container = document.getElementById('directories-container');
        const emptyState = document.getElementById('directories-empty-state');

        if (!container) {
            console.error('找不到目录容器元素 #directories-container');
            return;
        }

        if (directories.length === 0) {
            container.classList.add('hidden');
            if (emptyState) emptyState.classList.remove('hidden');
            return;
        }

        container.classList.remove('hidden');
        if (emptyState) emptyState.classList.add('hidden');

        try {
            const directoriesHtml = directories.map((directory) => {
                return this.createDirectoryCard(directory);
            }).join('');

            container.innerHTML = directoriesHtml;

            // 绑定卡片事件
            this.bindCardEvents();
        } catch (error) {
            console.error('渲染目录卡片时出错:', error);
        }
    }

    /**
     * 创建目录卡片
     */
    createDirectoryCard(directory) {
        try {
            const isSelected = this.selectedDirectories.has(directory.id);
            const statusBadgeClass = directory.enabled ? 'badge-success' : 'badge-error';
            const statusText = directory.enabled ? '启用' : '禁用';

            const cardHtml = `
                <div class="card bg-base-100 shadow-md border border-base-300 hover:shadow-lg hover:border-primary/30 transition-all duration-200 ${isSelected ? 'ring-2 ring-primary ring-opacity-50' : ''}" data-directory-id="${directory.id}">
                    <div class="card-body p-4">
                        <!-- 卡片头部：路径和选择框 -->
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center gap-2 flex-1 min-w-0">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-primary/10 text-primary rounded-lg flex items-center justify-center">
                                        <i class="bi bi-folder" aria-label="目录图标"></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-semibold text-base leading-tight truncate" title="${directory.name || '未命名目录'}">${directory.name || '未命名目录'}</h3>
                                    <p class="text-xs text-base-content/60 mt-1 line-clamp-1" title="${directory.path || '未知路径'}">${directory.path || '未知路径'}</p>
                                </div>
                            </div>
                            <div class="flex-shrink-0 ml-2">
                                <input type="checkbox" class="checkbox checkbox-primary checkbox-sm directory-checkbox"
                                       ${isSelected ? 'checked' : ''} data-directory-id="${directory.id}">
                            </div>
                        </div>

                        <!-- 状态和统计信息 -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <div class="badge ${statusBadgeClass} badge-sm">${statusText}</div>
                                <div class="text-xs text-base-content/50">
                                    ${(directory.file_count || 0).toLocaleString()} 文件
                                </div>
                            </div>
                            <div class="text-xs text-base-content/50">
                                ${directory.last_scan ?
                                    utils.formatDate(directory.last_scan, 'MM-DD HH:mm') :
                                    '未扫描'
                                }
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex gap-1">
                            <button type="button" class="btn btn-xs btn-primary flex-1 edit-directory-btn"
                                    data-directory-id="${directory.id}"
                                    title="编辑目录">
                                <i class="bi bi-pencil" style="font-size: 0.75rem;" aria-label="编辑图标"></i>
                                编辑
                            </button>
                            <button type="button" class="btn btn-xs btn-success flex-1 scan-directory-btn"
                                    data-directory-id="${directory.id}" ${!directory.enabled ? 'disabled' : ''}
                                    title="增量扫描（只处理新增或修改的文件）">
                                <i class="bi bi-arrow-repeat" style="font-size: 0.75rem;" aria-label="增量扫描图标"></i>
                                增量扫描
                            </button>

                            <div class="dropdown dropdown-end">
                                <div tabindex="0" role="button" class="btn btn-xs btn-ghost">
                                    <i class="bi bi-three-dots-vertical" style="font-size: 0.75rem;" aria-label="更多操作图标"></i>
                                </div>
                                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-36 p-2 shadow-lg border border-base-300">
                                    <li>
                                        <button type="button" class="full-scan-directory-btn text-xs"
                                                data-directory-id="${directory.id}" ${!directory.enabled ? 'disabled' : ''}>
                                            <i class="bi bi-arrow-clockwise" style="font-size: 0.75rem;" aria-label="全量扫描图标"></i>
                                            全量扫描
                                        </button>
                                    </li>
                                    <li><div class="divider my-1"></div></li>
                                    <li>
                                        <button type="button" class="toggle-directory-btn text-xs"
                                                data-directory-id="${directory.id}" data-enabled="${directory.enabled}">
                                            <i class="bi bi-power" style="font-size: 0.75rem;" aria-label="启用停用图标"></i>
                                            ${directory.enabled ? '停用' : '启用'}
                                        </button>
                                    </li>
                                    <li>
                                        <button type="button" class="delete-directory-btn text-xs text-error"
                                                data-directory-id="${directory.id}">
                                            <i class="bi bi-trash" style="font-size: 0.75rem;" aria-label="删除图标"></i>
                                            删除
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return cardHtml;

        } catch (error) {
            console.error('创建目录卡片时出错:', error);

            // 返回一个简单的错误卡片
            return `
                <div class="card bg-base-100 shadow-lg border border-error">
                    <div class="card-body">
                        <h3 class="card-title text-error">卡片生成错误</h3>
                        <p>目录ID: ${directory.id}</p>
                        <p>错误: ${error.message}</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 绑定卡片事件
     */
    bindCardEvents() {
        // 绑定复选框事件
        const checkboxes = document.querySelectorAll('.directory-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                if (e.target.checked) {
                    this.selectedDirectories.add(directoryId);
                } else {
                    this.selectedDirectories.delete(directoryId);
                }
                this.updateBatchScanButton();
            });
        });

        // 绑定编辑按钮事件
        const editButtons = document.querySelectorAll('.edit-directory-btn');

        editButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.editDirectory(directoryId);
            });
        });

        // 绑定删除按钮事件
        const deleteButtons = document.querySelectorAll('.delete-directory-btn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.deleteDirectory(directoryId);
            });
        });

        // 绑定增量扫描按钮事件
        const scanButtons = document.querySelectorAll('.scan-directory-btn');

        scanButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.scanDirectory(directoryId);
            });
        });

        // 绑定全量扫描按钮事件
        const fullScanButtons = document.querySelectorAll('.full-scan-directory-btn');

        fullScanButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                this.fullScanDirectory(directoryId);
            });
        });

        // 绑定启用/停用按钮事件
        const toggleButtons = document.querySelectorAll('.toggle-directory-btn');

        toggleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const directoryId = parseInt(e.target.dataset.directoryId);
                const enabled = e.target.dataset.enabled === 'true';
                this.toggleDirectory(directoryId, !enabled);
            });
        });


    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loading = document.getElementById('directories-loading');
        const container = document.getElementById('directories-container');
        const emptyState = document.getElementById('directories-empty-state');

        if (loading) loading.classList.remove('hidden');
        if (container) container.classList.add('hidden');
        if (emptyState) emptyState.classList.add('hidden');
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loading = document.getElementById('directories-loading');
        if (loading) loading.classList.add('hidden');
    }

    /**
     * 显示错误状态
     */
    showError() {
        const loading = document.getElementById('directories-loading');
        const container = document.getElementById('directories-container');
        const emptyState = document.getElementById('directories-empty-state');

        if (loading) loading.classList.add('hidden');
        if (container) container.classList.add('hidden');
        if (emptyState) emptyState.classList.remove('hidden');
    }

    /**
     * 显示目录模态框
     */
    showDirectoryModal(directory = null) {
        this.currentDirectory = directory;
        const modal = document.getElementById('directory-modal');
        const title = document.getElementById('directory-modal-title');
        const form = document.getElementById('directory-form');

        if (!modal || !title || !form) {
            return;
        }

        // 设置标题
        title.textContent = directory ? '编辑目录' : '添加目录';

        // 重置表单
        form.reset();
        this.clearFormErrors();

        // 如果是编辑模式，填充表单数据
        if (directory) {
            document.getElementById('directory-name').value = directory.name || '';
            document.getElementById('directory-path').value = directory.path || '';
            document.getElementById('directory-enabled').checked = directory.enabled !== false;
        }

        // 显示模态框
        modal.showModal();
    }

    /**
     * 保存目录
     */
    async saveDirectory() {
        const form = document.getElementById('directory-form');
        const submitBtn = document.getElementById('directory-submit-btn');
        const spinner = submitBtn.querySelector('.loading');

        if (!form || !submitBtn) {
            return;
        }

        const formData = new FormData(form);

        // 清除之前的错误
        this.clearFormErrors();

        // 验证表单
        if (!this.validateForm(formData)) {
            return;
        }

        try {
            submitBtn.disabled = true;
            if (spinner) spinner.classList.remove('hidden');

            const directoryData = {
                name: formData.get('name'),
                path: formData.get('path'),
                enabled: formData.has('enabled')
            };

            let response;
            if (this.currentDirectory) {
                // 更新现有目录
                response = await fetch(`/api/directories/${this.currentDirectory.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(directoryData)
                });
            } else {
                // 添加新目录
                response = await fetch('/api/directories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(directoryData)
                });
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // 显示成功消息
                if (window.toast) {
                    window.toast.success(result.message || '目录保存成功');
                }

                // 关闭模态框
                document.getElementById('directory-modal').close();

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(result.message || '保存失败');
            }

        } catch (error) {
            console.error('保存目录时出错:', error);
            handleApiError(error, '保存目录');
        } finally {
            submitBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 验证表单
     */
    validateForm(formData) {
        let isValid = true;
        const name = formData.get('name');
        const path = formData.get('path');

        // 验证目录名称
        if (!name || name.trim() === '') {
            this.showFieldError('directory-name', '请输入目录名称');
            isValid = false;
        } else if (name.trim().length < 1 || name.trim().length > 100) {
            this.showFieldError('directory-name', '目录名称长度必须在1-100字符之间');
            isValid = false;
        } else if (!/^[\w\s\u4e00-\u9fff-]+$/.test(name.trim())) {
            this.showFieldError('directory-name', '目录名称只能包含中文、英文、数字、空格、下划线和连字符');
            isValid = false;
        }

        // 验证目录路径
        if (!path || path.trim() === '') {
            this.showFieldError('directory-path', '请输入目录路径');
            isValid = false;
        }

        return isValid;
    }

    /**
     * 显示字段错误
     */
    showFieldError(fieldId, message) {
        const errorElement = document.getElementById(`${fieldId}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }
    }

    /**
     * 清除表单错误
     */
    clearFormErrors() {
        const errorElements = document.querySelectorAll('[id$="-error"]');
        errorElements.forEach(element => {
            element.textContent = '';
            element.classList.add('hidden');
        });
    }

    /**
     * 编辑目录
     */
    editDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (directory) {
            this.showDirectoryModal(directory);
        }
    }

    /**
     * 删除目录
     */
    deleteDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        // 设置删除确认信息
        const messageElement = document.getElementById('delete-directory-message');
        const warningElement = document.getElementById('delete-directory-warning');
        const movieCountElement = document.getElementById('directory-movie-count');

        if (messageElement) {
            messageElement.textContent = `您确定要删除目录 "${directory.path}" 吗？`;
        }

        if (directory.file_count > 0) {
            if (warningElement) warningElement.classList.remove('hidden');
            if (movieCountElement) movieCountElement.textContent = directory.file_count;
        } else {
            if (warningElement) warningElement.classList.add('hidden');
        }

        // 保存要删除的目录ID
        this.directoryToDelete = directoryId;

        // 显示删除确认模态框
        const modal = document.getElementById('delete-directory-modal');
        if (modal) {
            modal.showModal();
        }
    }

    /**
     * 确认删除目录
     */
    async confirmDelete() {
        if (!this.directoryToDelete) {
            return;
        }

        const confirmBtn = document.getElementById('confirm-delete-directory-btn');
        const spinner = confirmBtn?.querySelector('.loading');

        try {
            if (confirmBtn) confirmBtn.disabled = true;
            if (spinner) spinner.classList.remove('hidden');

            const response = await fetch(`/api/directories/${this.directoryToDelete}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // 显示成功消息
                if (window.toast) {
                    window.toast.success(result.message || '目录删除成功');
                }

                // 关闭模态框
                document.getElementById('delete-directory-modal').close();

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(result.message || '删除失败');
            }

        } catch (error) {
            console.error('删除目录时出错:', error);
            handleApiError(error, '删除目录');
        } finally {
            if (confirmBtn) confirmBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
            this.directoryToDelete = null;
        }
    }

    /**
     * 增量扫描目录
     */
    async scanDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        if (!directory.enabled) {
            if (window.toast) {
                window.toast.warning('目录已禁用，无法扫描');
            }
            return;
        }

        try {
            // 显示扫描中状态
            if (window.toast) {
                window.toast.info(`开始增量扫描目录: ${directory.path}`);
            }

            const response = await fetch(`/api/directories/${directoryId}/scan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ incremental: true })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // 显示扫描结果
                if (window.toast) {
                    const scanType = result.scan_type || '增量扫描';
                    window.toast.success(`${scanType}完成: 处理了 ${result.processed_files_count || 0} 个文件，新增 ${result.new_movies_count || 0} 部影片`);
                }

                // 重新加载目录列表以更新统计信息
                this.loadDirectories();
            } else {
                throw new Error(result.message || '扫描失败');
            }

        } catch (error) {
            console.error('增量扫描目录时出错:', error);
            handleApiError(error, '增量扫描目录');
        }
    }

    /**
     * 全量扫描目录
     */
    async fullScanDirectory(directoryId) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        if (!directory.enabled) {
            if (window.toast) {
                window.toast.warning('目录已禁用，无法扫描');
            }
            return;
        }

        try {
            // 显示扫描中状态
            if (window.toast) {
                window.toast.info(`开始全量扫描目录: ${directory.path}`);
            }

            const response = await fetch(`/api/directories/${directoryId}/scan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ incremental: false })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // 显示扫描结果
                if (window.toast) {
                    const scanType = result.scan_type || '全量扫描';
                    window.toast.success(`${scanType}完成: 处理了 ${result.processed_files_count || 0} 个文件，新增 ${result.new_movies_count || 0} 部影片`);
                }

                // 重新加载目录列表以更新统计信息
                this.loadDirectories();
            } else {
                throw new Error(result.message || '全量扫描失败');
            }

        } catch (error) {
            console.error('全量扫描目录时出错:', error);
            handleApiError(error, '全量扫描目录');
        }
    }

    /**
     * 切换目录启用状态
     */
    async toggleDirectory(directoryId, enabled) {
        const directory = this.directories.find(d => d.id === directoryId);
        if (!directory) {
            return;
        }

        try {
            const response = await fetch(`/api/directories/${directoryId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: directory.path,
                    description: directory.description,
                    enabled: enabled
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // 显示成功消息
                if (window.toast) {
                    window.toast.success(`目录已${enabled ? '启用' : '停用'}`);
                }

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(result.message || '操作失败');
            }

        } catch (error) {
            console.error('切换目录状态时出错:', error);
            handleApiError(error, '切换目录状态');
        }
    }

    /**
     * 处理搜索
     */
    handleSearch(query) {
        // 清除之前的搜索定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 设置新的搜索定时器
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    /**
     * 执行搜索
     */
    performSearch(query) {
        if (!query || query.trim() === '') {
            this.renderDirectories();
            return;
        }

        const searchTerm = query.toLowerCase().trim();
        const filteredDirectories = this.directories.filter(directory => {
            return (
                (directory.path && directory.path.toLowerCase().includes(searchTerm)) ||
                (directory.description && directory.description.toLowerCase().includes(searchTerm))
            );
        });


        this.renderDirectories(filteredDirectories);
    }

    /**
     * 更新批量扫描按钮状态
     */
    updateBatchScanButton() {
        const batchScanBtn = document.getElementById('batch-scan-btn');
        if (!batchScanBtn) return;

        if (this.selectedDirectories.size > 0) {
            batchScanBtn.classList.remove('hidden');
        } else {
            batchScanBtn.classList.add('hidden');
        }
    }

    /**
     * 处理批量扫描
     */
    async handleBatchScan() {
        if (this.selectedDirectories.size === 0) {
            if (window.toast) {
                window.toast.warning('请先选择要扫描的目录');
            }
            return;
        }

        const selectedIds = Array.from(this.selectedDirectories);
        const enabledDirectories = selectedIds.filter(id => {
            const directory = this.directories.find(d => d.id === id);
            return directory && directory.enabled;
        });

        if (enabledDirectories.length === 0) {
            if (window.toast) {
                window.toast.warning('选中的目录都已禁用，无法扫描');
            }
            return;
        }

        try {
            // 显示扫描中状态
            if (window.toast) {
                window.toast.info(`开始批量扫描 ${enabledDirectories.length} 个目录`);
            }

            const response = await fetch('/api/directories/scan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    directory_ids: enabledDirectories,
                    incremental: false
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // 显示扫描结果
                if (window.toast) {
                    const scanType = result.scan_type || '批量扫描';
                    window.toast.success(`${scanType}完成: 处理了 ${result.total_processed_files || 0} 个文件，新增 ${result.total_new_movies || 0} 部影片`);
                }

                // 清除选择状态
                this.selectedDirectories.clear();
                this.updateBatchScanButton();

                // 重新加载目录列表
                this.loadDirectories();
            } else {
                throw new Error(result.message || '批量扫描失败');
            }

        } catch (error) {
            console.error('批量扫描时出错:', error);
            handleApiError(error, '批量扫描');
        }
    }
}

// 页面加载完成后初始化目录管理器
document.addEventListener('DOMContentLoaded', () => {
    new DirectoriesManager();
});
