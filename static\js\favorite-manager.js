/**
 * 收藏功能管理器
 * 处理单个收藏、批量收藏和收藏状态管理
 */

class FavoriteManager {
    constructor() {
        this.favoriteStates = new Map(); // 缓存收藏状态
        this.batchMode = false; // 批量选择模式
        this.selectedMovies = new Set(); // 选中的影片ID
        
        this.init();
    }
    
    /**
     * 初始化收藏管理器
     */
    init() {
        // 绑定全局事件监听器
        this.bindGlobalEvents();
    }
    
    /**
     * 绑定全局事件监听器
     */
    bindGlobalEvents() {
        // 监听收藏按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.favorite-btn, .favorite-btn *')) {
                e.preventDefault();
                e.stopPropagation();
                
                const btn = e.target.closest('.favorite-btn');
                if (btn) {
                    const movieId = parseInt(btn.dataset.movieId);
                    if (movieId) {
                        this.toggleFavorite(movieId, btn);
                    }
                }
            }
            
            // 监听批量选择复选框
            if (e.target.matches('.batch-select-checkbox')) {
                const movieId = parseInt(e.target.dataset.movieId);
                if (movieId) {
                    this.toggleMovieSelection(movieId, e.target.checked);
                }
            }
        });
    }
    
    /**
     * 创建收藏按钮
     * @param {number} movieId - 影片ID
     * @param {boolean} isFavorited - 是否已收藏
     * @param {string} size - 按钮大小 ('sm', 'md', 'lg')
     * @returns {string} 按钮HTML
     */
    createFavoriteButton(movieId, isFavorited = false, size = 'md') {
        const sizeClass = size === 'sm' ? 'btn-sm' : size === 'lg' ? 'btn-lg' : '';
        const iconClass = isFavorited ? 'bi-heart-fill text-error' : 'bi-heart';
        const btnClass = isFavorited ? 'btn-outline' : 'btn-ghost';
        const title = isFavorited ? '取消收藏' : '收藏';
        
        return `
            <button type="button" 
                    class="btn ${btnClass} ${sizeClass} favorite-btn" 
                    data-movie-id="${movieId}"
                    title="${title}"
                    aria-label="${title}">
                <i class="bi ${iconClass}" style="font-size: 1.2rem;"></i>
            </button>
        `;
    }
    
    /**
     * 创建批量选择复选框
     * @param {number} movieId - 影片ID
     * @returns {string} 复选框HTML
     */
    createBatchSelectCheckbox(movieId) {
        const isSelected = this.selectedMovies.has(movieId);

        return `
            <div class="batch-select-container ${this.batchMode ? '' : 'hidden'}">
                <label class="cursor-pointer">
                    <input type="checkbox"
                           class="checkbox checkbox-primary batch-select-checkbox"
                           data-movie-id="${movieId}"
                           ${isSelected ? 'checked' : ''}>
                </label>
            </div>
        `;
    }

    /**
     * 创建收藏状态指示器（与收藏按钮位置完全对齐）
     * @param {number} movieId - 影片ID
     * @param {boolean} isFavorited - 是否已收藏
     * @param {string} size - 按钮大小 ('sm', 'md', 'lg')
     * @returns {string} 指示器HTML
     */
    createFavoriteStatusIndicator(movieId, isFavorited = false, size = 'sm') {
        if (!isFavorited) {
            return ''; // 未收藏时不显示指示器
        }

        // 使用与收藏按钮完全相同的尺寸和布局
        const sizeClass = size === 'sm' ? 'btn-sm' : size === 'lg' ? 'btn-lg' : '';
        const iconClass = 'bi-heart-fill text-error';

        // 模拟 btn 类的样式，但移除边框和背景，只保留尺寸和布局
        return `
            <div class="favorite-status-btn pointer-events-none inline-flex items-center justify-center ${sizeClass}"
                 data-movie-id="${movieId}"
                 title="已收藏"
                 aria-label="已收藏"
                 style="min-height: 2rem; height: 2rem; padding-left: 0.75rem; padding-right: 0.75rem; border-radius: 0.5rem; font-size: 0.875rem; line-height: 1.25rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.025em; min-width: 2rem;">
                <i class="bi ${iconClass}" style="font-size: 1.2rem;"></i>
            </div>
        `;
    }
    
    /**
     * 切换收藏状态
     * @param {number} movieId - 影片ID
     * @param {HTMLElement} btn - 按钮元素
     */
    async toggleFavorite(movieId, btn) {
        try {
            // 显示加载状态
            this.setButtonLoading(btn, true);
            
            // 调用API切换收藏状态
            const response = await api.toggleFavorite(movieId);
            
            if (response.success) {
                const data = response.data;
                const isFavorited = data.is_favorited;
                
                // 更新缓存状态
                this.favoriteStates.set(movieId, {
                    is_favorited: isFavorited,
                    favorite_id: data.favorite?.id || null,
                    favorited_at: data.favorite?.created_at || null
                });
                
                // 更新按钮状态
                this.updateFavoriteButton(btn, isFavorited);
                
                // 显示成功提示
                if (window.toast) {
                    window.toast.success(data.message);
                }
            } else {
                throw new Error(response.message || '操作失败');
            }
            
        } catch (error) {
            console.error('切换收藏状态失败:', error);
            handleApiError(error, '切换收藏状态');
        } finally {
            // 恢复按钮状态
            this.setButtonLoading(btn, false);
        }
    }
    
    /**
     * 更新收藏按钮状态
     * @param {HTMLElement} btn - 按钮元素
     * @param {boolean} isFavorited - 是否已收藏
     */
    updateFavoriteButton(btn, isFavorited) {
        const icon = btn.querySelector('i');

        if (isFavorited) {
            btn.className = btn.className.replace('btn-ghost', 'btn-outline');
            icon.className = 'bi bi-heart-fill text-error';
            btn.title = '取消收藏';
            btn.setAttribute('aria-label', '取消收藏');
        } else {
            btn.className = btn.className.replace('btn-outline', 'btn-ghost');
            icon.className = 'bi bi-heart';
            btn.title = '收藏';
            btn.setAttribute('aria-label', '收藏');
        }

        // 同时更新收藏状态指示器（如果存在）
        const movieId = btn.dataset.movieId;
        if (movieId) {
            this.updateFavoriteStatusIndicator(movieId, isFavorited);
        }
    }

    /**
     * 更新收藏状态指示器
     * @param {number} movieId - 影片ID
     * @param {boolean} isFavorited - 是否已收藏
     */
    updateFavoriteStatusIndicator(movieId, isFavorited) {
        const indicator = document.querySelector(`.favorite-status-indicator[data-movie-id="${movieId}"]`);
        if (indicator) {
            if (isFavorited) {
                // 使用与收藏按钮一致的外观
                const indicatorHtml = this.createFavoriteStatusIndicator(movieId, true, 'sm');
                indicator.innerHTML = indicatorHtml;
                indicator.classList.remove('hidden');
            } else {
                indicator.innerHTML = '';
                indicator.classList.add('hidden');
            }
        }
    }
    
    /**
     * 设置按钮加载状态
     * @param {HTMLElement} btn - 按钮元素
     * @param {boolean} loading - 是否加载中
     */
    setButtonLoading(btn, loading) {
        const icon = btn.querySelector('i');
        
        if (loading) {
            btn.disabled = true;
            icon.className = 'loading loading-spinner loading-sm';
        } else {
            btn.disabled = false;
            // 恢复图标会在updateFavoriteButton中处理
        }
    }
    
    /**
     * 获取收藏状态
     * @param {number} movieId - 影片ID
     * @returns {Promise<Object>} 收藏状态
     */
    async getFavoriteStatus(movieId) {
        try {
            // 先检查缓存
            if (this.favoriteStates.has(movieId)) {
                return this.favoriteStates.get(movieId);
            }
            
            // 从API获取状态
            const response = await api.getFavoriteStatus(movieId);
            if (response.success) {
                const status = response.data;
                this.favoriteStates.set(movieId, status);
                return status;
            }
            
            return { is_favorited: false, favorite_id: null, favorited_at: null };
            
        } catch (error) {
            console.error('获取收藏状态失败:', error);
            return { is_favorited: false, favorite_id: null, favorited_at: null };
        }
    }
    
    /**
     * 批量获取收藏状态
     * @param {number[]} movieIds - 影片ID列表
     * @returns {Promise<Map>} 收藏状态映射
     */
    async batchGetFavoriteStatus(movieIds) {
        const statusMap = new Map();
        
        // 分批获取状态以避免过多并发请求
        const batchSize = 10;
        for (let i = 0; i < movieIds.length; i += batchSize) {
            const batch = movieIds.slice(i, i + batchSize);
            const promises = batch.map(id => this.getFavoriteStatus(id));
            
            try {
                const results = await Promise.all(promises);
                batch.forEach((id, index) => {
                    statusMap.set(id, results[index]);
                });
            } catch (error) {
                console.error('批量获取收藏状态失败:', error);
                // 为失败的批次设置默认状态
                batch.forEach(id => {
                    statusMap.set(id, { is_favorited: false, favorite_id: null, favorited_at: null });
                });
            }
        }
        
        return statusMap;
    }
    
    /**
     * 切换批量选择模式
     * @param {boolean} enabled - 是否启用批量模式
     */
    toggleBatchMode(enabled) {
        this.batchMode = enabled;
        
        // 显示/隐藏批量选择复选框
        const checkboxContainers = document.querySelectorAll('.batch-select-container');
        checkboxContainers.forEach(container => {
            if (enabled) {
                container.classList.remove('hidden');
            } else {
                container.classList.add('hidden');
            }
        });
        
        // 清空选择
        if (!enabled) {
            this.clearSelection();
        }
        
        // 触发批量模式变更事件
        document.dispatchEvent(new CustomEvent('batchModeChanged', {
            detail: { enabled, selectedCount: this.selectedMovies.size }
        }));
    }
    
    /**
     * 切换影片选择状态
     * @param {number} movieId - 影片ID
     * @param {boolean} selected - 是否选中
     */
    toggleMovieSelection(movieId, selected) {
        if (selected) {
            this.selectedMovies.add(movieId);
        } else {
            this.selectedMovies.delete(movieId);
        }
        
        // 触发选择变更事件
        document.dispatchEvent(new CustomEvent('movieSelectionChanged', {
            detail: { 
                movieId, 
                selected, 
                selectedCount: this.selectedMovies.size,
                selectedMovies: Array.from(this.selectedMovies)
            }
        }));
    }
    
    /**
     * 全选/取消全选
     * @param {boolean} selectAll - 是否全选
     * @param {number[]} movieIds - 可选的影片ID列表，如果不提供则使用页面上的所有复选框
     */
    toggleSelectAll(selectAll, movieIds = null) {
        if (movieIds) {
            // 使用提供的影片ID列表
            if (selectAll) {
                movieIds.forEach(id => this.selectedMovies.add(id));
            } else {
                movieIds.forEach(id => this.selectedMovies.delete(id));
            }
        } else {
            // 使用页面上的复选框
            const checkboxes = document.querySelectorAll('.batch-select-checkbox');
            checkboxes.forEach(checkbox => {
                const movieId = parseInt(checkbox.dataset.movieId);
                checkbox.checked = selectAll;
                this.toggleMovieSelection(movieId, selectAll);
            });
        }
        
        // 更新页面上的复选框状态
        this.updateCheckboxStates();
    }
    
    /**
     * 更新复选框状态
     */
    updateCheckboxStates() {
        const checkboxes = document.querySelectorAll('.batch-select-checkbox');
        checkboxes.forEach(checkbox => {
            const movieId = parseInt(checkbox.dataset.movieId);
            checkbox.checked = this.selectedMovies.has(movieId);
        });
    }
    
    /**
     * 清空选择
     */
    clearSelection() {
        this.selectedMovies.clear();
        this.updateCheckboxStates();
        
        // 触发选择变更事件
        document.dispatchEvent(new CustomEvent('movieSelectionChanged', {
            detail: { 
                selectedCount: 0,
                selectedMovies: []
            }
        }));
    }
    
    /**
     * 批量收藏选中的影片
     * @returns {Promise<Object>} 批量收藏结果
     */
    async batchAddFavorites() {
        if (this.selectedMovies.size === 0) {
            if (window.toast) {
                window.toast.warning('请先选择要收藏的影片');
            }
            return;
        }
        
        try {
            const movieIds = Array.from(this.selectedMovies);
            const response = await api.batchAddFavorites(movieIds);
            
            if (response.success) {
                const data = response.data;
                
                // 更新缓存状态
                data.success_movie_ids.forEach(movieId => {
                    this.favoriteStates.set(movieId, {
                        is_favorited: true,
                        favorite_id: null, // 批量操作不返回具体ID
                        favorited_at: new Date().toISOString()
                    });
                });
                
                // 更新页面上的收藏按钮
                this.updateFavoriteButtonsForMovies(data.success_movie_ids, true);
                
                // 显示结果提示
                if (window.toast) {
                    window.toast.success(response.message);
                }
                
                // 清空选择
                this.clearSelection();
                
                return data;
            } else {
                throw new Error(response.message || '批量收藏失败');
            }
            
        } catch (error) {
            console.error('批量收藏失败:', error);
            handleApiError(error, '批量收藏');
            throw error;
        }
    }

    /**
     * 批量移除收藏选中的影片
     * @returns {Promise<Object>} 批量移除收藏结果
     */
    async batchRemoveFavorites() {
        if (this.selectedMovies.size === 0) {
            if (window.toast) {
                window.toast.warning('请先选择要移除收藏的影片');
            }
            return;
        }

        try {
            const movieIds = Array.from(this.selectedMovies);
            const response = await api.batchRemoveFavorites(movieIds);

            if (response.success) {
                const data = response.data;

                // 更新缓存状态
                data.success_movie_ids.forEach(movieId => {
                    this.favoriteStates.set(movieId, {
                        is_favorited: false,
                        favorite_id: null,
                        favorited_at: null
                    });
                });

                // 更新页面上的收藏按钮
                this.updateFavoriteButtonsForMovies(data.success_movie_ids, false);

                // 显示结果提示
                if (window.toast) {
                    window.toast.success(response.message);
                }

                // 清空选择
                this.clearSelection();

                return data;
            } else {
                throw new Error(response.message || '批量移除收藏失败');
            }

        } catch (error) {
            console.error('批量移除收藏失败:', error);
            handleApiError(error, '批量移除收藏');
            throw error;
        }
    }

    /**
     * 更新指定影片的收藏按钮状态
     * @param {number[]} movieIds - 影片ID列表
     * @param {boolean} isFavorited - 是否已收藏
     */
    updateFavoriteButtonsForMovies(movieIds, isFavorited) {
        movieIds.forEach(movieId => {
            const btn = document.querySelector(`.favorite-btn[data-movie-id="${movieId}"]`);
            if (btn) {
                this.updateFavoriteButton(btn, isFavorited);
            }

            // 直接更新状态指示器
            this.updateFavoriteStatusIndicator(movieId, isFavorited);
        });
    }
    
    /**
     * 获取选中的影片数量
     * @returns {number} 选中数量
     */
    getSelectedCount() {
        return this.selectedMovies.size;
    }
    
    /**
     * 获取选中的影片ID列表
     * @returns {number[]} 影片ID列表
     */
    getSelectedMovies() {
        return Array.from(this.selectedMovies);
    }
    
    /**
     * 是否处于批量模式
     * @returns {boolean} 是否批量模式
     */
    isBatchMode() {
        return this.batchMode;
    }
}

// 创建全局收藏管理器实例
window.favoriteManager = new FavoriteManager();
