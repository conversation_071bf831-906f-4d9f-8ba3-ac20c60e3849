<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 450" style="background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);">
  <!-- 背景 -->
  <rect width="300" height="450" fill="url(#bg-gradient)"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f9fafb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="icon-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6b7280;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 边框 -->
  <rect x="2" y="2" width="296" height="446" fill="none" stroke="#d1d5db" stroke-width="2" stroke-dasharray="8,4"/>
  
  <!-- 影片图标 -->
  <g transform="translate(150, 180)">
    <!-- 胶片卷轴 -->
    <rect x="-40" y="-30" width="80" height="60" rx="8" fill="url(#icon-gradient)" opacity="0.8"/>
    
    <!-- 胶片孔 -->
    <circle cx="-25" cy="-15" r="3" fill="#f9fafb"/>
    <circle cx="-25" cy="0" r="3" fill="#f9fafb"/>
    <circle cx="-25" cy="15" r="3" fill="#f9fafb"/>
    <circle cx="25" cy="-15" r="3" fill="#f9fafb"/>
    <circle cx="25" cy="0" r="3" fill="#f9fafb"/>
    <circle cx="25" cy="15" r="3" fill="#f9fafb"/>
    
    <!-- 播放按钮 -->
    <circle cx="0" cy="0" r="18" fill="#ffffff" opacity="0.9"/>
    <polygon points="-6,-8 -6,8 10,0" fill="url(#icon-gradient)"/>
  </g>
  
  <!-- 文字 -->
  <text x="150" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="500" fill="#6b7280">
    暂无海报
  </text>
  <text x="150" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">
    No Poster Available
  </text>
  
  <!-- 装饰性元素 -->
  <g opacity="0.3">
    <circle cx="50" cy="80" r="2" fill="#d1d5db"/>
    <circle cx="250" cy="120" r="1.5" fill="#d1d5db"/>
    <circle cx="80" cy="350" r="1" fill="#d1d5db"/>
    <circle cx="220" cy="380" r="2" fill="#d1d5db"/>
  </g>
</svg>
