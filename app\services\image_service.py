"""
图片处理服务
提供图片拼接、压缩、格式转换等功能
"""
import base64
import io
import logging
from typing import List, Optional, Tuple
from PIL import Image, ImageDraw
import requests
from pathlib import Path

logger = logging.getLogger(__name__)


class ImageService:
    """图片处理服务类"""
    
    # 目录封面配置
    COVER_WIDTH = 350
    COVER_HEIGHT = 197
    POSTER_COUNT = 4  # 拼接海报数量
    POSTER_WIDTH = 87.5  # 每张海报的宽度 (350 ÷ 4)
    POSTER_HEIGHT = 197  # 每张海报的高度
    POSTER_SPACING = 0  # 海报之间的间距
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def load_image_from_path(self, image_path: str) -> Optional[Image.Image]:
        """
        从文件路径加载图片
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            PIL Image对象，失败返回None
        """
        try:
            if not Path(image_path).exists():
                logger.warning(f"图片文件不存在: {image_path}")
                return None
                
            with Image.open(image_path) as img:
                # 转换为RGB模式，确保兼容性
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                return img.copy()
                
        except Exception as e:
            logger.error(f"加载图片失败 {image_path}: {e}")
            return None
    
    def load_image_from_url(self, image_url: str) -> Optional[Image.Image]:
        """
        从URL加载图片
        
        Args:
            image_url: 图片URL
            
        Returns:
            PIL Image对象，失败返回None
        """
        try:
            response = self.session.get(image_url, timeout=10)
            response.raise_for_status()
            
            with Image.open(io.BytesIO(response.content)) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                return img.copy()
                
        except Exception as e:
            logger.error(f"从URL加载图片失败 {image_url}: {e}")
            return None
    
    def resize_and_crop_poster(self, image: Image.Image, target_width: int, target_height: int) -> Image.Image:
        """
        调整海报尺寸并裁剪以适应目标尺寸

        Args:
            image: 原始图片
            target_width: 目标宽度
            target_height: 目标高度

        Returns:
            处理后的图片
        """
        try:
            # 计算缩放比例，保持宽高比
            img_ratio = image.width / image.height
            target_ratio = target_width / target_height

            # 优先保证填满目标区域，可能会裁剪部分内容
            if img_ratio > target_ratio:
                # 图片更宽，按高度缩放，裁剪左右两边
                new_height = target_height
                new_width = int(target_height * img_ratio)
            else:
                # 图片更高，按宽度缩放，裁剪上下两边
                new_width = target_width
                new_height = int(target_width / img_ratio)

            # 缩放图片
            resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 居中裁剪
            left = (new_width - target_width) // 2
            top = (new_height - target_height) // 2
            right = left + target_width
            bottom = top + target_height

            cropped = resized.crop((left, top, right, bottom))
            return cropped

        except Exception as e:
            logger.error(f"调整图片尺寸失败: {e}")
            # 返回纯色占位图片
            return self.create_placeholder_image(target_width, target_height)
    
    def create_placeholder_image(self, width: int, height: int, color: str = '#2a2a2a') -> Image.Image:
        """
        创建占位图片
        
        Args:
            width: 宽度
            height: 高度
            color: 背景颜色
            
        Returns:
            占位图片
        """
        try:
            img = Image.new('RGB', (width, height), color)
            draw = ImageDraw.Draw(img)
            
            # 绘制简单的文件夹图标
            icon_size = min(width, height) // 3
            icon_x = (width - icon_size) // 2
            icon_y = (height - icon_size) // 2
            
            # 绘制文件夹形状
            draw.rectangle(
                [icon_x, icon_y + icon_size//4, icon_x + icon_size, icon_y + icon_size],
                fill='#4a4a4a',
                outline='#6a6a6a'
            )
            draw.rectangle(
                [icon_x, icon_y, icon_x + icon_size//2, icon_y + icon_size//2],
                fill='#4a4a4a',
                outline='#6a6a6a'
            )
            
            return img
            
        except Exception as e:
            logger.error(f"创建占位图片失败: {e}")
            return Image.new('RGB', (width, height), '#2a2a2a')
    
    def create_directory_cover(self, poster_paths: List[str]) -> Optional[str]:
        """
        创建目录封面图片

        Args:
            poster_paths: 海报文件路径列表（最多3张）

        Returns:
            base64编码的封面图片，失败返回None
        """
        try:
            # 创建空白画布
            cover = Image.new('RGB', (self.COVER_WIDTH, self.COVER_HEIGHT), '#1a1a1a')

            # 如果没有海报，返回默认封面
            if not poster_paths:
                placeholder = self.create_placeholder_image(self.COVER_WIDTH, self.COVER_HEIGHT)
                result = self.image_to_base64(placeholder)
                return result

            # 限制最多4张海报
            poster_paths = poster_paths[:self.POSTER_COUNT]

            # 加载和处理海报
            processed_posters = []
            for poster_path in poster_paths:
                poster_img = self.load_image_from_path(poster_path)
                if poster_img:
                    # 调整海报尺寸
                    resized_poster = self.resize_and_crop_poster(
                        poster_img, self.POSTER_WIDTH, self.POSTER_HEIGHT
                    )
                    processed_posters.append(resized_poster)
                else:
                    logger.warning(f"海报加载失败: {poster_path}")

            # 如果没有成功加载任何海报，返回占位图片
            if not processed_posters:
                placeholder = self.create_placeholder_image(self.COVER_WIDTH, self.COVER_HEIGHT)
                result = self.image_to_base64(placeholder)
                return result

            # 如果海报数量不足4张，重复使用现有海报
            while len(processed_posters) < self.POSTER_COUNT:
                processed_posters.append(processed_posters[-1])

            # 拼接海报
            for i, poster in enumerate(processed_posters[:self.POSTER_COUNT]):
                x_offset = int(i * self.POSTER_WIDTH)
                cover.paste(poster, (x_offset, 0))

            # 转换为base64
            result = self.image_to_base64(cover)
            return result

        except Exception as e:
            logger.error(f"创建目录封面失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return None
    
    def image_to_base64(self, image: Image.Image, format: str = 'JPEG', quality: int = 85) -> str:
        """
        将PIL图片转换为base64编码

        Args:
            image: PIL图片对象
            format: 图片格式
            quality: 图片质量（1-100）

        Returns:
            base64编码的图片字符串
        """
        try:
            buffer = io.BytesIO()
            image.save(buffer, format=format, quality=quality, optimize=True)
            img_bytes = buffer.getvalue()

            img_base64 = base64.b64encode(img_bytes).decode('utf-8')

            result = f"data:image/{format.lower()};base64,{img_base64}"
            return result

        except Exception as e:
            logger.error(f"图片转base64失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return ""
    
    def base64_to_image(self, base64_str: str) -> Optional[Image.Image]:
        """
        将base64编码转换为PIL图片
        
        Args:
            base64_str: base64编码的图片字符串
            
        Returns:
            PIL图片对象，失败返回None
        """
        try:
            # 移除data:image前缀
            if base64_str.startswith('data:image'):
                base64_str = base64_str.split(',')[1]
            
            img_bytes = base64.b64decode(base64_str)
            return Image.open(io.BytesIO(img_bytes))
            
        except Exception as e:
            logger.error(f"base64转图片失败: {e}")
            return None
