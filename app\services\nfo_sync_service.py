"""
NFO 同步服务
用于将数据库中的影片信息同步回NFO文件
"""
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import logging
import os
import shutil
from app.models.models import Movie

logger = logging.getLogger(__name__)


class NFOSyncService:
    """NFO 同步服务类"""
    
    @staticmethod
    def sync_movie_to_nfo(movie: Movie) -> Dict[str, Any]:
        """
        将数据库中的影片信息同步到NFO文件
        
        Args:
            movie: 影片对象
            
        Returns:
            同步结果字典，包含成功状态、更新字段等信息
        """
        try:
            # 检查NFO文件路径
            nfo_path = NFOSyncService._get_nfo_path(movie)
            if not nfo_path:
                return {
                    'success': False,
                    'message': '无法确定NFO文件路径',
                    'updated_fields': []
                }
            
            # 检查文件权限
            permission_check = NFOSyncService._check_file_permissions(nfo_path)
            if not permission_check['success']:
                return permission_check
            
            # 读取现有NFO文件或创建新的
            if Path(nfo_path).exists():
                tree, root = NFOSyncService._load_existing_nfo(nfo_path)
                if tree is None:
                    return {
                        'success': False,
                        'message': 'NFO文件格式错误，无法解析',
                        'updated_fields': []
                    }
            else:
                tree, root = NFOSyncService._create_new_nfo()
            
            # 比较并更新字段
            updated_fields = NFOSyncService._update_nfo_fields(root, movie)
            
            # 如果有更新，保存文件
            if updated_fields:
                NFOSyncService._save_nfo_file(tree, nfo_path)
                return {
                    'success': True,
                    'message': f'成功同步 {len(updated_fields)} 个字段到NFO文件',
                    'updated_fields': updated_fields,
                    'nfo_path': nfo_path
                }
            else:
                return {
                    'success': True,
                    'message': 'NFO文件已是最新，无需更新',
                    'updated_fields': [],
                    'nfo_path': nfo_path
                }
                
        except Exception as e:
            logger.error(f"同步NFO文件时发生错误: {e}")
            return {
                'success': False,
                'message': f'同步失败: {str(e)}',
                'updated_fields': []
            }
    
    @staticmethod
    def _get_nfo_path(movie: Movie) -> Optional[str]:
        """
        获取NFO文件路径
        
        Args:
            movie: 影片对象
            
        Returns:
            NFO文件路径，如果无法确定则返回None
        """
        # 如果数据库中有NFO路径记录，直接使用
        if movie.nfo_path and Path(movie.nfo_path).exists():
            return movie.nfo_path
        
        # 根据影片文件路径推断NFO路径
        if movie.file_path:
            video_path = Path(movie.file_path)
            if video_path.exists():
                nfo_path = video_path.with_suffix('.nfo')
                return str(nfo_path)
        
        return None
    
    @staticmethod
    def _check_file_permissions(nfo_path: str) -> Dict[str, Any]:
        """
        检查文件权限
        
        Args:
            nfo_path: NFO文件路径
            
        Returns:
            权限检查结果
        """
        try:
            nfo_file = Path(nfo_path)
            
            # 验证路径安全性
            if not NFOSyncService._is_safe_path(nfo_path):
                return {
                    'success': False,
                    'message': '文件路径不安全',
                    'updated_fields': []
                }
            
            # 检查目录是否存在
            if not nfo_file.parent.exists():
                return {
                    'success': False,
                    'message': '目录不存在',
                    'updated_fields': []
                }
            
            # 检查写入权限
            if nfo_file.exists():
                if not os.access(nfo_path, os.W_OK):
                    return {
                        'success': False,
                        'message': '没有文件写入权限',
                        'updated_fields': []
                    }
            else:
                if not os.access(nfo_file.parent, os.W_OK):
                    return {
                        'success': False,
                        'message': '没有目录写入权限',
                        'updated_fields': []
                    }
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"检查文件权限时发生错误: {e}")
            return {
                'success': False,
                'message': f'权限检查失败: {str(e)}',
                'updated_fields': []
            }
    
    @staticmethod
    def _is_safe_path(file_path: str) -> bool:
        """
        验证文件路径安全性，防止路径遍历攻击
        
        Args:
            file_path: 文件路径
            
        Returns:
            路径是否安全
        """
        try:
            # 解析路径
            resolved_path = Path(file_path).resolve()
            
            # 检查是否包含危险的路径组件
            dangerous_patterns = ['..', '~', '$']
            path_str = str(resolved_path)
            
            for pattern in dangerous_patterns:
                if pattern in path_str:
                    return False
            
            # 检查文件扩展名
            if not path_str.lower().endswith('.nfo'):
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def _load_existing_nfo(nfo_path: str) -> Tuple[Optional[ET.ElementTree], Optional[ET.Element]]:
        """
        加载现有的NFO文件
        
        Args:
            nfo_path: NFO文件路径
            
        Returns:
            (ElementTree对象, 根元素) 或 (None, None)
        """
        try:
            tree = ET.parse(nfo_path)
            root = tree.getroot()
            
            if root.tag != 'movie':
                logger.error(f"NFO文件格式错误，根元素不是 'movie': {nfo_path}")
                return None, None
            
            return tree, root
            
        except ET.ParseError as e:
            logger.error(f"NFO文件解析错误 {nfo_path}: {e}")
            return None, None
        except Exception as e:
            logger.error(f"加载NFO文件时发生错误 {nfo_path}: {e}")
            return None, None
    
    @staticmethod
    def _create_new_nfo() -> Tuple[ET.ElementTree, ET.Element]:
        """
        创建新的NFO文件结构
        
        Returns:
            (ElementTree对象, 根元素)
        """
        root = ET.Element('movie')
        tree = ET.ElementTree(root)
        return tree, root
    
    @staticmethod
    def _save_nfo_file(tree: ET.ElementTree, nfo_path: str) -> None:
        """
        保存NFO文件
        
        Args:
            tree: ElementTree对象
            nfo_path: NFO文件路径
        """
        # 格式化XML
        NFOSyncService._indent_xml(tree.getroot())
        
        # 保存文件
        tree.write(nfo_path, encoding='utf-8', xml_declaration=True)
        logger.info(f"NFO文件已保存: {nfo_path}")
    
    @staticmethod
    def _update_nfo_fields(root: ET.Element, movie: Movie) -> List[str]:
        """
        更新NFO文件字段

        Args:
            root: NFO文件根元素
            movie: 影片对象

        Returns:
            更新的字段列表
        """
        updated_fields = []

        # 基本文本字段映射
        text_fields = {
            'title': movie.title,
            'originaltitle': movie.original_title,
            'plot': movie.plot,
            'outline': movie.outline,
            'country': movie.country,
            'sorttitle': movie.sort_title,
            'trailer': movie.trailer,
            'num': movie.num
        }

        for xml_field, db_value in text_fields.items():
            if db_value is not None:
                if NFOSyncService._update_text_field(root, xml_field, str(db_value)):
                    updated_fields.append(xml_field)

        # 数值字段映射
        numeric_fields = {
            'year': movie.year,
            'runtime': movie.runtime,
            'rating': movie.rating,
            'criticrating': movie.critic_rating
        }

        for xml_field, db_value in numeric_fields.items():
            if db_value is not None:
                if NFOSyncService._update_text_field(root, xml_field, str(db_value)):
                    updated_fields.append(xml_field)

        # 布尔字段
        if movie.lock_data is not None:
            if NFOSyncService._update_text_field(root, 'lockdata', str(movie.lock_data).lower()):
                updated_fields.append('lockdata')

        # 日期字段
        date_fields = {
            'dateadded': movie.date_added,
            'premiered': movie.premiered,
            'releasedate': movie.release_date
        }

        for xml_field, db_value in date_fields.items():
            if db_value is not None:
                # 对于premiered和releasedate，只使用日期部分（YYYY-MM-DD）
                # 对于dateadded，保留完整的日期时间格式
                if xml_field in ['premiered', 'releasedate']:
                    date_str = db_value.strftime('%Y-%m-%d') if hasattr(db_value, 'strftime') else str(db_value)
                else:
                    date_str = db_value.strftime('%Y-%m-%d %H:%M:%S') if hasattr(db_value, 'strftime') else str(db_value)

                if NFOSyncService._update_text_field(root, xml_field, date_str):
                    updated_fields.append(xml_field)

        # 更新分类
        if NFOSyncService._update_list_field(root, 'genre', [genre.name for genre in movie.genres]):
            updated_fields.append('genres')

        # 更新标签
        if NFOSyncService._update_list_field(root, 'tag', [tag.name for tag in movie.tags]):
            updated_fields.append('tags')

        # 更新演员
        if NFOSyncService._update_actors_field(root, movie.actors):
            updated_fields.append('actors')

        # 更新系列
        if movie.series:
            if NFOSyncService._update_series_field(root, movie.series.name):
                updated_fields.append('series')

        return updated_fields

    @staticmethod
    def _update_text_field(root: ET.Element, field_name: str, new_value: str) -> bool:
        """
        更新文本字段

        Args:
            root: NFO文件根元素
            field_name: 字段名
            new_value: 新值

        Returns:
            是否有更新
        """
        element = root.find(field_name)

        if element is None:
            # 字段不存在，创建新字段
            element = ET.SubElement(root, field_name)
            element.text = new_value
            return True
        else:
            # 字段存在，检查是否需要更新
            current_value = element.text or ''
            if current_value.strip() != new_value.strip():
                element.text = new_value
                return True

        return False

    @staticmethod
    def _update_list_field(root: ET.Element, field_name: str, new_values: List[str]) -> bool:
        """
        更新列表字段（如分类、标签）

        Args:
            root: NFO文件根元素
            field_name: 字段名
            new_values: 新值列表

        Returns:
            是否有更新
        """
        # 获取现有值
        existing_elements = root.findall(field_name)
        existing_values = [elem.text.strip() for elem in existing_elements if elem.text]

        # 比较值
        new_values_set = set(new_values)
        existing_values_set = set(existing_values)

        if new_values_set == existing_values_set:
            return False

        # 删除所有现有元素
        for elem in existing_elements:
            root.remove(elem)

        # 添加新元素
        for value in new_values:
            if value.strip():
                elem = ET.SubElement(root, field_name)
                elem.text = value.strip()

        return True

    @staticmethod
    def _update_actors_field(root: ET.Element, actors) -> bool:
        """
        更新演员字段

        Args:
            root: NFO文件根元素
            actors: 演员列表

        Returns:
            是否有更新
        """
        # 获取现有演员
        existing_actors = root.findall('actor')
        existing_actor_data = []

        for actor_elem in existing_actors:
            name_elem = actor_elem.find('name')
            role_elem = actor_elem.find('role')
            type_elem = actor_elem.find('type')

            if name_elem is not None and name_elem.text:
                existing_actor_data.append({
                    'name': name_elem.text.strip(),
                    'role': role_elem.text.strip() if role_elem is not None and role_elem.text else '',
                    'type': type_elem.text.strip() if type_elem is not None and type_elem.text else ''
                })

        # 构建新演员数据
        new_actor_data = []
        for actor in actors:
            new_actor_data.append({
                'name': actor.name,
                'role': actor.role or '',
                'type': actor.actor_type or 'Actor'
            })

        # 比较演员数据
        if NFOSyncService._compare_actor_lists(existing_actor_data, new_actor_data):
            return False

        # 删除所有现有演员元素
        for actor_elem in existing_actors:
            root.remove(actor_elem)

        # 添加新演员元素
        for actor_data in new_actor_data:
            actor_elem = ET.SubElement(root, 'actor')

            # 使用 'name' 标签，这是演员字段的标准格式
            name_elem = ET.SubElement(actor_elem, 'name')
            name_elem.text = actor_data['name']

            if actor_data['role']:
                role_elem = ET.SubElement(actor_elem, 'role')
                role_elem.text = actor_data['role']

            if actor_data['type']:
                type_elem = ET.SubElement(actor_elem, 'type')
                type_elem.text = actor_data['type']

        return True

    @staticmethod
    def _update_series_field(root: ET.Element, series_name: str) -> bool:
        """
        更新系列字段

        Args:
            root: NFO文件根元素
            series_name: 系列名称

        Returns:
            是否有更新
        """
        set_elem = root.find('set')

        if set_elem is None:
            # 系列元素不存在，创建新的
            set_elem = ET.SubElement(root, 'set')
            name_elem = ET.SubElement(set_elem, 'name')
            name_elem.text = series_name
            return True
        else:
            # 系列元素存在，检查name子元素
            name_elem = set_elem.find('name')

            if name_elem is None:
                # 没有name子元素，创建一个
                # 先清理set元素的直接文本内容（如果有的话）
                set_elem.text = None
                name_elem = ET.SubElement(set_elem, 'name')
                name_elem.text = series_name
                return True
            else:
                # 有name子元素，检查内容
                current_name = name_elem.text.strip() if name_elem.text else ''

                if current_name != series_name.strip():
                    name_elem.text = series_name
                    return True

        return False

    @staticmethod
    def _compare_actor_lists(existing: List[Dict], new: List[Dict]) -> bool:
        """
        比较演员列表是否相同

        Args:
            existing: 现有演员列表
            new: 新演员列表

        Returns:
            是否相同
        """
        if len(existing) != len(new):
            return False

        # 按名称排序后比较
        existing_sorted = sorted(existing, key=lambda x: x['name'])
        new_sorted = sorted(new, key=lambda x: x['name'])

        for i in range(len(existing_sorted)):
            if (existing_sorted[i]['name'] != new_sorted[i]['name'] or
                existing_sorted[i]['role'] != new_sorted[i]['role'] or
                existing_sorted[i]['type'] != new_sorted[i]['type']):
                return False

        return True

    @staticmethod
    def _indent_xml(elem: ET.Element, level: int = 0) -> None:
        """
        格式化XML缩进

        Args:
            elem: XML元素
            level: 缩进级别
        """
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                NFOSyncService._indent_xml(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i
