"""
目录管理 API 路由
提供媒体目录的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel as PydanticBaseModel
from app.core.database import get_db
from app.services.directory_service import DirectoryService
from app.models.models import Directory
import logging
import os
from pathlib import Path
import platform
from typing import Optional, List

logger = logging.getLogger(__name__)

# Pydantic 模型
class DirectoryCreate(PydanticBaseModel):
    name: str
    path: str
    enabled: bool = True

class DirectoryUpdate(PydanticBaseModel):
    name: str = None
    path: str = None
    enabled: bool = None

class DirectoryScanRequest(PydanticBaseModel):
    directory_ids: list[int]
    incremental: bool = True

class DirectoryItem(PydanticBaseModel):
    name: str
    path: str
    is_directory: bool
    size: Optional[int] = None
    modified_time: Optional[str] = None

class DirectoryBrowseRequest(PydanticBaseModel):
    path: Optional[str] = None

# 创建路由器
router = APIRouter(
    prefix="/directories",
    tags=["目录管理"],
    responses={404: {"description": "目录不存在"}}
)


@router.post("")
async def create_directory(directory: DirectoryCreate, db: Session = Depends(get_db)):
    """创建新的媒体目录"""
    try:
        # 验证输入数据
        if not directory.name or not directory.name.strip():
            raise HTTPException(status_code=400, detail="目录名称不能为空")

        if not directory.path or not directory.path.strip():
            raise HTTPException(status_code=400, detail="目录路径不能为空")

        # 验证目录名称格式
        name = directory.name.strip()
        if len(name) < 1 or len(name) > 100:
            raise HTTPException(status_code=400, detail="目录名称长度必须在1-100字符之间")

        # 检查特殊字符（允许中文、英文、数字、空格、下划线、连字符）
        import re
        if not re.match(r'^[\w\s\u4e00-\u9fff-]+$', name):
            raise HTTPException(status_code=400, detail="目录名称只能包含中文、英文、数字、空格、下划线和连字符")

        dir_service = DirectoryService(db)
        new_directory = dir_service.create_directory(
            path=directory.path.strip(),
            name=name,
            enabled=directory.enabled
        )

        if new_directory:
            return {
                "success": True,
                "message": "目录创建成功",
                "data": {
                    "id": new_directory.id,
                    "name": new_directory.name,
                    "path": new_directory.path,
                    "enabled": new_directory.enabled,
                    "file_count": 0,
                    "last_scan": None,
                    "created_at": new_directory.created_at.isoformat() if new_directory.created_at else None
                }
            }
        else:
            raise HTTPException(status_code=400, detail="目录创建失败")

    except ValueError as e:
        # 处理名称或路径重复的错误
        logger.warning(f"创建目录验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    except Exception as e:
        logger.error(f"创建目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建目录失败: {str(e)}")


@router.get("")
async def get_directories(
    enabled_only: bool = Query(False, description="是否只返回启用的目录"),
    with_cover: bool = Query(False, description="是否包含封面图片"),
    db: Session = Depends(get_db)
):
    """获取所有目录"""
    try:
        dir_service = DirectoryService(db)
        directories = dir_service.get_all_directories(enabled_only=enabled_only)

        # 获取每个目录的文件数量
        from app.services.database_service import DatabaseService
        db_service = DatabaseService(db)

        directory_list = []
        for d in directories:
            # 获取该目录下的影片数量
            file_count = db_service.get_movies_count_by_directory(d.id)

            directory_data = {
                "id": d.id,
                "name": d.name,
                "path": d.path,
                "enabled": d.enabled,
                "file_count": file_count,
                "last_scan": d.last_scan_time.isoformat() if d.last_scan_time else None,
                "created_at": d.created_at.isoformat() if d.created_at else None
            }

            # 如果需要封面图片，添加封面数据
            if with_cover:
                directory_data["cover_image_base64"] = d.cover_image_base64
                directory_data["cover_generated_at"] = d.cover_generated_at.isoformat() if d.cover_generated_at else None

            directory_list.append(directory_data)

        return {
            "success": True,
            "data": directory_list,
            "count": len(directory_list)
        }

    except Exception as e:
        logger.error(f"获取目录列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取目录列表失败: {str(e)}")



@router.post("/regenerate-all-covers")
async def regenerate_all_covers(db: Session = Depends(get_db)):
    """重新生成所有目录的封面图片"""
    try:
        dir_service = DirectoryService(db)
        result = dir_service.regenerate_all_covers()

        return {
            "success": True,
            "message": "封面重新生成完成",
            "data": result
        }

    except Exception as e:
        logger.error(f"重新生成所有封面时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"重新生成封面失败: {str(e)}")


@router.post("/browse")
async def browse_directories(request: DirectoryBrowseRequest):
    """
    浏览服务器目录结构

    Args:
        request: 包含要浏览的目录路径的请求体

    Returns:
        目录列表和当前路径信息
    """
    try:
        # 获取安全的可浏览路径
        safe_paths = _get_safe_browsable_paths()

        # 从请求体中获取路径
        path = request.path

        # 如果没有指定路径，返回根目录列表
        if not path:
            return {
                "success": True,
                "data": {
                    "current_path": "",
                    "parent_path": None,
                    "items": [
                        {
                            "name": os.path.basename(safe_path) or safe_path,
                            "path": safe_path,
                            "is_directory": True,
                            "modified_time": None
                        }
                        for safe_path in safe_paths
                    ],
                    "breadcrumbs": [{"name": "根目录", "path": "", "is_current": True}],
                    "total_count": len(safe_paths)
                }
            }

        # 规范化路径
        normalized_path = os.path.normpath(path)

        # 检查路径是否在安全范围内
        if not _is_path_safe(normalized_path, safe_paths):
            raise HTTPException(status_code=403, detail="无权访问此路径")

        # 检查路径是否存在
        if not os.path.exists(normalized_path):
            raise HTTPException(status_code=404, detail="路径不存在")

        # 检查是否为目录
        if not os.path.isdir(normalized_path):
            raise HTTPException(status_code=400, detail="指定路径不是目录")

        # 获取目录内容
        items = []
        try:
            for item_name in os.listdir(normalized_path):
                item_path = os.path.join(normalized_path, item_name)

                # 跳过隐藏文件和系统文件
                if item_name.startswith('.'):
                    continue

                # 只处理目录
                if os.path.isdir(item_path):
                    try:
                        stat_info = os.stat(item_path)
                        items.append(DirectoryItem(
                            name=item_name,
                            path=item_path,
                            is_directory=True,
                            modified_time=str(int(stat_info.st_mtime))
                        ))
                    except (OSError, PermissionError):
                        # 跳过无法访问的目录
                        continue

        except PermissionError:
            raise HTTPException(status_code=403, detail="无权限访问此目录")

        # 按名称排序
        items.sort(key=lambda x: x.name.lower())

        # 生成面包屑导航
        breadcrumbs = _generate_breadcrumbs(normalized_path)

        # 获取父目录路径
        parent_path = os.path.dirname(normalized_path) if normalized_path != "/" else None
        if parent_path and not _is_path_safe(parent_path, safe_paths):
            parent_path = None

        return {
            "success": True,
            "data": {
                "current_path": normalized_path,
                "parent_path": parent_path,
                "items": [item.dict() for item in items],
                "breadcrumbs": breadcrumbs,
                "total_count": len(items)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"浏览目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"浏览目录失败: {str(e)}")


@router.get("/{directory_id}")
async def get_directory(directory_id: int, db: Session = Depends(get_db)):
    """获取指定目录"""
    try:
        dir_service = DirectoryService(db)
        directory = dir_service.get_directory_by_id(directory_id)
        
        if not directory:
            raise HTTPException(status_code=404, detail="目录不存在")
        
        return {
            "success": True,
            "directory": {
                "id": directory.id,
                "path": directory.path,
                "name": directory.name,
                "enabled": directory.enabled,
                "last_scan_time": directory.last_scan_time,
                "created_at": directory.created_at,
                "updated_at": directory.updated_at
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取目录失败: {str(e)}")


@router.put("/{directory_id}")
async def update_directory(directory_id: int, directory: DirectoryUpdate, db: Session = Depends(get_db)):
    """更新目录"""
    try:
        # 验证输入数据
        if directory.name is not None:
            name = directory.name.strip()
            if not name:
                raise HTTPException(status_code=400, detail="目录名称不能为空")
            if len(name) < 1 or len(name) > 100:
                raise HTTPException(status_code=400, detail="目录名称长度必须在1-100字符之间")

            # 检查特殊字符
            import re
            if not re.match(r'^[\w\s\u4e00-\u9fff-]+$', name):
                raise HTTPException(status_code=400, detail="目录名称只能包含中文、英文、数字、空格、下划线和连字符")

        if directory.path is not None and not directory.path.strip():
            raise HTTPException(status_code=400, detail="目录路径不能为空")

        dir_service = DirectoryService(db)

        # 构建更新数据
        update_data = {}
        if directory.path is not None:
            update_data['path'] = directory.path.strip()
        if directory.name is not None:
            update_data['name'] = directory.name.strip()
        if directory.enabled is not None:
            update_data['enabled'] = directory.enabled

        updated_directory = dir_service.update_directory(directory_id, **update_data)

        if not updated_directory:
            raise HTTPException(status_code=404, detail="目录不存在或更新失败")

        # 获取文件数量
        from app.services.database_service import DatabaseService
        db_service = DatabaseService(db)
        file_count = db_service.get_movies_count_by_directory(updated_directory.path)

        return {
            "success": True,
            "message": "目录更新成功",
            "data": {
                "id": updated_directory.id,
                "name": updated_directory.name,
                "path": updated_directory.path,
                "enabled": updated_directory.enabled,
                "file_count": file_count,
                "last_scan": updated_directory.last_scan_time.isoformat() if updated_directory.last_scan_time else None,
                "created_at": updated_directory.created_at.isoformat() if updated_directory.created_at else None
            }
        }

    except ValueError as e:
        # 处理名称或路径重复的错误
        logger.warning(f"更新目录验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新目录失败: {str(e)}")


@router.delete("/{directory_id}")
async def delete_directory(directory_id: int, db: Session = Depends(get_db)):
    """
    删除目录

    删除目录配置及其关联的所有影片数据，但不删除磁盘上的实际文件

    Args:
        directory_id: 目录ID
    """
    try:
        dir_service = DirectoryService(db)

        # 获取目录信息
        directory = dir_service.get_directory_by_id(directory_id)
        if not directory:
            raise HTTPException(status_code=404, detail="目录不存在")

        # 获取目录下的影片数量
        from app.services.database_service import DatabaseService
        db_service = DatabaseService(db)
        movie_count = db_service.get_movies_count_by_directory(directory.path)

        # 执行删除（默认删除关联数据）
        success = dir_service.delete_directory(directory_id)

        if not success:
            raise HTTPException(status_code=500, detail="删除目录失败")

        message = f"目录删除成功"
        if movie_count > 0:
            message += f"，同时删除了 {movie_count} 部影片的数据记录"

        return {
            "success": True,
            "message": message,
            "deleted_movies_count": movie_count
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除目录失败: {str(e)}")


class ScanRequest(PydanticBaseModel):
    incremental: bool = True

@router.post("/{directory_id}/scan")
async def scan_directory(
    directory_id: int,
    request: ScanRequest,
    db: Session = Depends(get_db)
):
    """
    扫描指定目录

    Args:
        directory_id: 目录ID
        request: 扫描请求参数，包含是否增量扫描
        db: 数据库会话

    Returns:
        扫描结果，包含处理的文件数、新增的影片数等统计信息
    """
    try:
        dir_service = DirectoryService(db)
        directory = dir_service.get_directory_by_id(directory_id)

        if not directory:
            raise HTTPException(status_code=404, detail="目录不存在")

        if not directory.enabled:
            raise HTTPException(status_code=400, detail="目录已禁用，无法扫描")

        # 导入扫描相关服务
        from app.services.media_scanner import MediaScanner
        from app.services.nfo_parser import NFOParser
        from app.services.database_service import DatabaseService
        from app.services.mapping_service import MappingService

        # 刷新映射规则缓存以确保使用最新的映射规则
        mapping_service = MappingService(db)
        mapping_service.refresh_cache()
        logger.info("映射规则缓存已刷新，开始扫描")

        # 执行扫描
        scanner = MediaScanner()
        media_files = scanner.scan_directory(directory.path, recursive=True)
        summary = scanner.get_scan_summary()

        # 增量扫描逻辑
        incremental = request.incremental
        if incremental and directory.last_scan_time:
            # 过滤出修改时间晚于上次扫描时间的文件
            from pathlib import Path
            import os
            filtered_files = []
            for media_file in media_files:
                file_path = Path(media_file['file_path'])
                if file_path.exists():
                    file_mtime = os.path.getmtime(file_path)
                    if file_mtime > directory.last_scan_time.timestamp():
                        filtered_files.append(media_file)
            media_files = filtered_files
            logger.info(f"增量扫描：过滤后剩余 {len(media_files)} 个文件需要处理")

        # 解析 NFO 文件并保存到数据库
        parsed_files = []
        saved_movies = []
        updated_movies = []
        skipped_files = []

        db_service = DatabaseService(db)

        for media_file in media_files:
            if media_file['nfo_path']:
                movie_data = NFOParser.parse_nfo_file(media_file['nfo_path'])
                if movie_data:
                    movie_data['file_info'] = media_file
                    parsed_files.append(movie_data)

                    # 检查是否已存在相同文件路径的影片
                    existing_movie = db_service.get_movie_by_file_path(media_file['file_path'])

                    if existing_movie:
                        # 更新现有影片
                        updated_movie = db_service.update_movie_data(existing_movie.id, movie_data, media_file)
                        if updated_movie:
                            updated_movies.append({
                                "id": updated_movie.id,
                                "title": updated_movie.title,
                                "year": updated_movie.year,
                                "file_path": updated_movie.file_path
                            })
                    else:
                        # 保存新影片到数据库
                        saved_movie = db_service.save_movie_data(movie_data, media_file)
                        if saved_movie:
                            saved_movies.append({
                                "id": saved_movie.id,
                                "title": saved_movie.title,
                                "year": saved_movie.year,
                                "file_path": saved_movie.file_path
                            })
            else:
                # 没有NFO文件的媒体文件
                skipped_files.append(media_file['file_path'])

        # 生成目录封面（在更新扫描时间之前）
        cover_generated = False
        cover_message = ""
        try:
            cover_generated = dir_service.generate_directory_cover(directory_id)
            cover_message = "封面生成成功" if cover_generated else "封面生成失败"
            logger.info(f"目录 '{directory.name}' {cover_message}")
        except Exception as cover_error:
            cover_message = f"封面生成异常: {str(cover_error)}"
            logger.error(f"目录 '{directory.name}' {cover_message}")

        # 更新目录扫描时间
        dir_service.update_scan_time(directory_id)

        scan_type = "增量扫描" if incremental else "全量扫描"

        return {
            "success": True,
            "message": f"目录 '{directory.name}' {scan_type}完成",
            "scan_type": scan_type,
            "summary": summary,
            "processed_files_count": len(media_files),
            "parsed_count": len(parsed_files),
            "new_movies_count": len(saved_movies),
            "updated_movies_count": len(updated_movies),
            "skipped_files_count": len(skipped_files),
            "cover_generated": cover_generated,
            "cover_message": cover_message,
            "new_movies": saved_movies,
            "updated_movies": updated_movies,
            "skipped_files": skipped_files[:5]  # 只返回前5个跳过的文件作为示例
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"扫描目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"扫描目录失败: {str(e)}")


@router.post("/scan-all")
async def scan_all_directories(
    request: ScanRequest,
    db: Session = Depends(get_db)
):
    """
    扫描所有启用的目录

    Args:
        incremental: 是否增量扫描（默认false为全量扫描）
        db: 数据库会话

    Returns:
        所有目录的扫描结果汇总
    """
    try:
        incremental = request.incremental
        dir_service = DirectoryService(db)
        directories = dir_service.get_all_directories(enabled_only=True)

        if not directories:
            return {
                "success": True,
                "message": "没有启用的目录需要扫描",
                "scan_type": "增量扫描" if incremental else "全量扫描",
                "scanned_directories": []
            }

        # 导入扫描相关服务
        from app.services.media_scanner import MediaScanner
        from app.services.nfo_parser import NFOParser
        from app.services.database_service import DatabaseService
        from app.services.mapping_service import MappingService

        # 刷新映射规则缓存以确保使用最新的映射规则
        mapping_service = MappingService(db)
        mapping_service.refresh_cache()
        logger.info("映射规则缓存已刷新，开始批量扫描")

        scanned_results = []
        total_new_movies = 0
        total_updated_movies = 0
        total_processed_files = 0

        scan_type = "增量扫描" if incremental else "全量扫描"

        for directory in directories:
            try:
                # 执行扫描
                scanner = MediaScanner()
                media_files = scanner.scan_directory(directory.path, recursive=True)
                summary = scanner.get_scan_summary()

                # 增量扫描逻辑
                if incremental and directory.last_scan_time:
                    # 过滤出修改时间晚于上次扫描时间的文件
                    from pathlib import Path
                    import os
                    filtered_files = []
                    for media_file in media_files:
                        file_path = Path(media_file['file_path'])
                        if file_path.exists():
                            file_mtime = os.path.getmtime(file_path)
                            if file_mtime > directory.last_scan_time.timestamp():
                                filtered_files.append(media_file)
                    media_files = filtered_files

                # 解析 NFO 文件并保存到数据库
                parsed_files = []
                saved_movies = []
                updated_movies = []

                db_service = DatabaseService(db)

                for media_file in media_files:
                    if media_file['nfo_path']:
                        movie_data = NFOParser.parse_nfo_file(media_file['nfo_path'])
                        if movie_data:
                            movie_data['file_info'] = media_file
                            parsed_files.append(movie_data)

                            # 检查是否已存在相同文件路径的影片
                            existing_movie = db_service.get_movie_by_file_path(media_file['file_path'])

                            if existing_movie:
                                # 更新现有影片
                                updated_movie = db_service.update_movie_data(existing_movie.id, movie_data, media_file)
                                if updated_movie:
                                    updated_movies.append({
                                        "id": updated_movie.id,
                                        "title": updated_movie.title,
                                        "year": updated_movie.year,
                                        "file_path": updated_movie.file_path
                                    })
                            else:
                                # 保存新影片到数据库
                                saved_movie = db_service.save_movie_data(movie_data, media_file)
                                if saved_movie:
                                    saved_movies.append({
                                        "id": saved_movie.id,
                                        "title": saved_movie.title,
                                        "year": saved_movie.year,
                                        "file_path": saved_movie.file_path
                                    })

                # 生成目录封面（在更新扫描时间之前）
                cover_generated = False
                cover_message = ""
                try:
                    cover_generated = dir_service.generate_directory_cover(directory.id)
                    cover_message = "封面生成成功" if cover_generated else "封面生成失败"
                    logger.info(f"目录 '{directory.name}' {cover_message}")
                except Exception as cover_error:
                    cover_message = f"封面生成异常: {str(cover_error)}"
                    logger.error(f"目录 '{directory.name}' {cover_message}")

                # 更新目录扫描时间
                dir_service.update_scan_time(directory.id)

                scanned_results.append({
                    "directory_id": directory.id,
                    "directory_name": directory.name,
                    "directory_path": directory.path,
                    "success": True,
                    "summary": summary,
                    "processed_files_count": len(media_files),
                    "parsed_count": len(parsed_files),
                    "new_movies_count": len(saved_movies),
                    "updated_movies_count": len(updated_movies),
                    "cover_generated": cover_generated,
                    "cover_message": cover_message
                })

                total_new_movies += len(saved_movies)
                total_updated_movies += len(updated_movies)
                total_processed_files += len(media_files)

            except Exception as e:
                logger.error(f"扫描目录 {directory.name} 时发生错误: {e}")
                scanned_results.append({
                    "directory_id": directory.id,
                    "directory_name": directory.name,
                    "directory_path": directory.path,
                    "success": False,
                    "error": str(e)
                })

        return {
            "success": True,
            "message": f"{scan_type}完成，共处理 {len(directories)} 个目录",
            "scan_type": scan_type,
            "total_directories": len(directories),
            "total_processed_files": total_processed_files,
            "total_new_movies": total_new_movies,
            "total_updated_movies": total_updated_movies,
            "scanned_directories": scanned_results
        }

    except Exception as e:
        logger.error(f"扫描全部目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"扫描全部目录失败: {str(e)}")


@router.post("/scan")
async def scan_directories(
    request: DirectoryScanRequest,
    db: Session = Depends(get_db)
):
    """
    批量扫描指定目录

    Args:
        request: 包含要扫描的目录ID列表
        incremental: 是否增量扫描（默认false为全量扫描）
        db: 数据库会话

    Returns:
        批量扫描结果
    """
    try:
        incremental = request.incremental
        if not request.directory_ids:
            raise HTTPException(status_code=400, detail="请提供要扫描的目录ID列表")

        dir_service = DirectoryService(db)

        # 获取要扫描的目录
        directories = []
        for directory_id in request.directory_ids:
            directory = dir_service.get_directory_by_id(directory_id)
            if directory and directory.enabled:
                directories.append(directory)

        if not directories:
            return {
                "success": True,
                "message": "没有有效的目录需要扫描",
                "scan_type": "增量扫描" if incremental else "全量扫描",
                "scanned_directories": []
            }

        # 导入扫描相关服务
        from app.services.media_scanner import MediaScanner
        from app.services.nfo_parser import NFOParser
        from app.services.database_service import DatabaseService
        from app.services.mapping_service import MappingService

        # 刷新映射规则缓存以确保使用最新的映射规则
        mapping_service = MappingService(db)
        mapping_service.refresh_cache()
        logger.info("映射规则缓存已刷新，开始批量扫描指定目录")

        scanned_results = []
        total_new_movies = 0
        total_updated_movies = 0
        total_processed_files = 0

        scan_type = "增量扫描" if incremental else "全量扫描"

        for directory in directories:
            try:
                # 执行扫描
                scanner = MediaScanner()
                media_files = scanner.scan_directory(directory.path, recursive=True)
                summary = scanner.get_scan_summary()

                # 增量扫描逻辑
                if incremental and directory.last_scan_time:
                    from pathlib import Path
                    import os
                    filtered_files = []
                    for media_file in media_files:
                        file_path = Path(media_file['file_path'])
                        if file_path.exists():
                            file_mtime = os.path.getmtime(file_path)
                            if file_mtime > directory.last_scan_time.timestamp():
                                filtered_files.append(media_file)
                    media_files = filtered_files

                # 解析 NFO 文件并保存到数据库
                parsed_files = []
                saved_movies = []
                updated_movies = []

                db_service = DatabaseService(db)

                for media_file in media_files:
                    if media_file['nfo_path']:
                        movie_data = NFOParser.parse_nfo_file(media_file['nfo_path'])
                        if movie_data:
                            movie_data['file_info'] = media_file
                            parsed_files.append(movie_data)

                            # 检查是否已存在相同文件路径的影片
                            existing_movie = db_service.get_movie_by_file_path(media_file['file_path'])

                            if existing_movie:
                                # 更新现有影片
                                updated_movie = db_service.update_movie_data(existing_movie.id, movie_data, media_file)
                                if updated_movie:
                                    updated_movies.append({
                                        "id": updated_movie.id,
                                        "title": updated_movie.title,
                                        "year": updated_movie.year,
                                        "file_path": updated_movie.file_path
                                    })
                            else:
                                # 保存新影片到数据库
                                saved_movie = db_service.save_movie_data(movie_data, media_file)
                                if saved_movie:
                                    saved_movies.append({
                                        "id": saved_movie.id,
                                        "title": saved_movie.title,
                                        "year": saved_movie.year,
                                        "file_path": saved_movie.file_path
                                    })

                # 生成目录封面（在更新扫描时间之前）
                cover_generated = False
                cover_message = ""
                try:
                    cover_generated = dir_service.generate_directory_cover(directory.id)
                    cover_message = "封面生成成功" if cover_generated else "封面生成失败"
                    logger.info(f"目录 '{directory.name}' {cover_message}")
                except Exception as cover_error:
                    cover_message = f"封面生成异常: {str(cover_error)}"
                    logger.error(f"目录 '{directory.name}' {cover_message}")

                # 更新目录扫描时间
                dir_service.update_scan_time(directory.id)

                scanned_results.append({
                    "directory_id": directory.id,
                    "directory_name": directory.name,
                    "directory_path": directory.path,
                    "success": True,
                    "summary": summary,
                    "processed_files_count": len(media_files),
                    "parsed_count": len(parsed_files),
                    "new_movies_count": len(saved_movies),
                    "updated_movies_count": len(updated_movies),
                    "cover_generated": cover_generated,
                    "cover_message": cover_message
                })

                total_new_movies += len(saved_movies)
                total_updated_movies += len(updated_movies)
                total_processed_files += len(media_files)

            except Exception as e:
                logger.error(f"扫描目录 {directory.name} 时发生错误: {e}")
                scanned_results.append({
                    "directory_id": directory.id,
                    "directory_name": directory.name,
                    "directory_path": directory.path,
                    "success": False,
                    "error": str(e)
                })

        return {
            "success": True,
            "message": f"批量{scan_type}完成，共处理 {len(directories)} 个目录",
            "scan_type": scan_type,
            "total_directories": len(directories),
            "total_processed_files": total_processed_files,
            "total_new_movies": total_new_movies,
            "total_updated_movies": total_updated_movies,
            "scanned_directories": scanned_results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量扫描目录时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量扫描目录失败: {str(e)}")


@router.get("/stats")
async def get_directory_stats(db: Session = Depends(get_db)):
    """获取目录统计信息"""
    try:
        dir_service = DirectoryService(db)
        stats = dir_service.get_directory_stats()

        return {
            "success": True,
            "stats": stats
        }

    except Exception as e:
        logger.error(f"获取目录统计信息时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


def _get_safe_browsable_paths() -> List[str]:
    """
    获取安全的可浏览路径列表
    根据操作系统返回不同的安全路径
    """
    system = platform.system().lower()

    if system == "windows":
        # Windows系统的安全路径
        safe_paths = [
            "C:\\Users",
            "D:\\",
            "E:\\",
            "F:\\",
            "G:\\",
            "H:\\",
            "I:\\",
            "J:\\",
            "K:\\",
            "L:\\",
            "M:\\",
            "N:\\",
            "O:\\",
            "P:\\",
            "Q:\\",
            "R:\\",
            "S:\\",
            "T:\\",
            "U:\\",
            "V:\\",
            "W:\\",
            "X:\\",
            "Y:\\",
            "Z:\\"
        ]
        # 过滤存在的驱动器
        safe_paths = [path for path in safe_paths if os.path.exists(path)]
    else:
        # Linux/macOS系统的安全路径
        safe_paths = [
            "/home",
            "/media",
            "/mnt",
            "/opt",
            "/srv",
            "/var/lib",
            "/usr/local"
        ]
        # 过滤存在的路径
        safe_paths = [path for path in safe_paths if os.path.exists(path)]

        # 添加用户主目录
        home_dir = os.path.expanduser("~")
        if home_dir and os.path.exists(home_dir):
            safe_paths.append(home_dir)

    return safe_paths


def _is_path_safe(path: str, safe_paths: List[str]) -> bool:
    """
    检查路径是否在安全范围内
    """
    normalized_path = os.path.normpath(os.path.abspath(path))

    for safe_path in safe_paths:
        safe_normalized = os.path.normpath(os.path.abspath(safe_path))
        if normalized_path.startswith(safe_normalized):
            return True

    return False


def _generate_breadcrumbs(path: str) -> List[dict]:
    """
    生成面包屑导航
    """
    breadcrumbs = []
    parts = []

    # 分割路径
    if platform.system().lower() == "windows":
        # Windows路径处理
        if path.startswith("\\\\"):
            # UNC路径
            parts = path.split("\\")
        else:
            # 普通Windows路径
            drive, rest = os.path.splitdrive(path)
            if drive:
                parts = [drive] + rest.split("\\")
            else:
                parts = path.split("\\")
    else:
        # Unix-like路径处理
        parts = path.split("/")

    # 构建面包屑
    current_path = ""
    for i, part in enumerate(parts):
        if not part and i > 0:  # 跳过空部分（除了根目录）
            continue

        if i == 0:
            if platform.system().lower() == "windows":
                current_path = part if part else "\\"
                display_name = part if part else "根目录"
            else:
                current_path = "/"
                display_name = "根目录"
        else:
            if platform.system().lower() == "windows":
                current_path = os.path.join(current_path, part)
            else:
                current_path = current_path.rstrip("/") + "/" + part
            display_name = part

        breadcrumbs.append({
            "name": display_name,
            "path": current_path,
            "is_current": i == len(parts) - 1
        })

    return breadcrumbs



