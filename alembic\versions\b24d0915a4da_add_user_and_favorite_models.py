"""Add user and favorite models

Revision ID: b24d0915a4da
Revises: 08cbc911e430
Create Date: 2025-07-30 17:36:44.356237

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b24d0915a4da'
down_revision: Union[str, Sequence[str], None] = '08cbc911e430'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # SQLite doesn't support adding constraints directly, skip this operation
    # The unique constraint on directories.name is already defined in the model
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # SQLite doesn't support dropping constraints directly, skip this operation
    pass
    # ### end Alembic commands ###
