/**
 * 批量操作工具栏组件
 * 提供批量选择、批量收藏等功能的UI控制
 */

class BatchToolbar {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.isVisible = false;
        this.selectedCount = 0;
        
        this.init();
    }
    
    /**
     * 初始化工具栏
     */
    init() {
        this.createToolbar();
        this.bindEvents();
    }
    
    /**
     * 创建工具栏HTML
     */
    createToolbar() {
        if (!this.container) return;
        
        const toolbarHtml = `
            <div class="batch-toolbar fixed bottom-16 left-1/2 transform -translate-x-1/2 z-50 hidden w-auto max-w-4xl px-4" id="batch-toolbar">
                <div class="bg-base-100 shadow-2xl rounded-2xl border border-base-300 w-full">
                    <!-- 桌面端布局 (≥768px) -->
                    <div class="hidden md:flex items-center justify-between gap-4 px-6 py-4">
                        <!-- 选择信息 -->
                        <div class="flex items-center gap-3">
                            <div class="badge badge-primary badge-lg">
                                <span id="selected-count">0</span> 已选择
                            </div>
                            <button type="button" class="btn btn-ghost btn-sm" id="select-all-btn">
                                全选
                            </button>
                            <button type="button" class="btn btn-ghost btn-sm" id="clear-selection-btn">
                                清空
                            </button>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex items-center gap-2">
                            <button type="button" class="btn btn-secondary btn-sm hidden" id="batch-edit-btn" disabled>
                                <i class="bi bi-pencil-square"></i>
                                批量编辑
                            </button>
                            <button type="button" class="btn btn-primary btn-sm hidden" id="batch-favorite-btn" disabled>
                                <i class="bi bi-heart-fill"></i>
                                批量收藏
                            </button>
                            <button type="button" class="btn btn-error btn-sm hidden" id="batch-remove-favorite-btn" disabled>
                                <i class="bi bi-heart-slash"></i>
                                批量移除收藏
                            </button>
                            <button type="button" class="btn btn-ghost btn-sm" id="close-batch-mode-btn">
                                <i class="bi bi-x-lg"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 移动端布局 (<768px) -->
                    <div class="md:hidden px-4 py-3">
                        <!-- 第一行：选择信息和关闭按钮 -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="badge badge-primary">
                                <span id="selected-count-mobile">0</span> 已选择
                            </div>
                            <button type="button" class="btn btn-ghost btn-xs" id="close-batch-mode-btn-mobile">
                                <i class="bi bi-x-lg"></i>
                            </button>
                        </div>

                        <!-- 第二行：操作按钮 -->
                        <div class="flex flex-wrap gap-2 justify-center">
                            <button type="button" class="btn btn-ghost btn-xs flex-1 min-w-0" id="select-all-btn-mobile">
                                全选
                            </button>
                            <button type="button" class="btn btn-ghost btn-xs flex-1 min-w-0" id="clear-selection-btn-mobile">
                                清空
                            </button>
                            <button type="button" class="btn btn-secondary btn-xs hidden flex-1 min-w-0" id="batch-edit-btn-mobile" disabled>
                                <i class="bi bi-pencil-square"></i>
                                编辑
                            </button>
                            <button type="button" class="btn btn-primary btn-xs hidden flex-1 min-w-0" id="batch-favorite-btn-mobile" disabled>
                                <i class="bi bi-heart-fill"></i>
                                收藏
                            </button>
                            <button type="button" class="btn btn-error btn-xs hidden flex-1 min-w-0" id="batch-remove-favorite-btn-mobile" disabled>
                                <i class="bi bi-heart-slash"></i>
                                移除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 将工具栏添加到页面底部
        document.body.insertAdjacentHTML('beforeend', toolbarHtml);
        
        // 缓存工具栏元素
        this.toolbar = document.getElementById('batch-toolbar');

        // 桌面端元素
        this.selectedCountEl = document.getElementById('selected-count');
        this.selectAllBtn = document.getElementById('select-all-btn');
        this.clearSelectionBtn = document.getElementById('clear-selection-btn');
        this.batchFavoriteBtn = document.getElementById('batch-favorite-btn');
        this.batchRemoveFavoriteBtn = document.getElementById('batch-remove-favorite-btn');
        this.batchEditBtn = document.getElementById('batch-edit-btn');
        this.closeBatchModeBtn = document.getElementById('close-batch-mode-btn');

        // 移动端元素
        this.selectedCountElMobile = document.getElementById('selected-count-mobile');
        this.selectAllBtnMobile = document.getElementById('select-all-btn-mobile');
        this.clearSelectionBtnMobile = document.getElementById('clear-selection-btn-mobile');
        this.batchFavoriteBtnMobile = document.getElementById('batch-favorite-btn-mobile');
        this.batchRemoveFavoriteBtnMobile = document.getElementById('batch-remove-favorite-btn-mobile');
        this.batchEditBtnMobile = document.getElementById('batch-edit-btn-mobile');
        this.closeBatchModeBtnMobile = document.getElementById('close-batch-mode-btn-mobile');
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        if (!this.toolbar) return;
        
        // 桌面端事件监听器
        this.selectAllBtn?.addEventListener('click', () => {
            this.handleSelectAll();
        });

        this.clearSelectionBtn?.addEventListener('click', () => {
            this.handleClearSelection();
        });

        this.batchFavoriteBtn?.addEventListener('click', () => {
            this.handleBatchFavorite();
        });

        this.batchRemoveFavoriteBtn?.addEventListener('click', () => {
            this.handleBatchRemoveFavorite();
        });

        this.batchEditBtn?.addEventListener('click', () => {
            if (window.batchEditor) {
                const selectedIds = window.favoriteManager.getSelectedMovies();
                window.batchEditor.open(selectedIds);
            }
        });

        this.closeBatchModeBtn?.addEventListener('click', () => {
            this.handleCloseBatchMode();
        });

        // 移动端事件监听器
        this.selectAllBtnMobile?.addEventListener('click', () => {
            this.handleSelectAll();
        });

        this.clearSelectionBtnMobile?.addEventListener('click', () => {
            this.handleClearSelection();
        });

        this.batchFavoriteBtnMobile?.addEventListener('click', () => {
            this.handleBatchFavorite();
        });

        this.batchRemoveFavoriteBtnMobile?.addEventListener('click', () => {
            this.handleBatchRemoveFavorite();
        });

        this.batchEditBtnMobile?.addEventListener('click', () => {
            if (window.batchEditor) {
                const selectedIds = window.favoriteManager.getSelectedMovies();
                window.batchEditor.open(selectedIds);
            }
        });

        this.closeBatchModeBtnMobile?.addEventListener('click', () => {
            this.handleCloseBatchMode();
        });
        
        // 监听收藏管理器的事件
        document.addEventListener('batchModeChanged', (e) => {
            this.handleBatchModeChanged(e.detail);
        });
        
        document.addEventListener('movieSelectionChanged', (e) => {
            this.handleSelectionChanged(e.detail);
        });
    }
    
    /**
     * 处理批量模式变更
     * @param {Object} detail - 事件详情
     */
    handleBatchModeChanged(detail) {
        if (detail.enabled) {
            this.show();
        } else {
            this.hide();
        }
        
        this.updateSelectedCount(detail.selectedCount || 0);
    }
    
    /**
     * 处理选择变更
     * @param {Object} detail - 事件详情
     */
    handleSelectionChanged(detail) {
        this.updateSelectedCount(detail.selectedCount);
        this.updateSelectAllButton();
        this.updateBatchButtons(detail.selectedMovies || []);
    }
    
    /**
     * 更新选中数量显示
     * @param {number} count - 选中数量
     */
    updateSelectedCount(count) {
        this.selectedCount = count;

        // 更新桌面端计数显示
        if (this.selectedCountEl) {
            this.selectedCountEl.textContent = count;
        }

        // 更新移动端计数显示
        if (this.selectedCountElMobile) {
            this.selectedCountElMobile.textContent = count;
        }
    }
    
    /**
     * 更新全选按钮状态
     */
    updateSelectAllButton() {
        if (!this.selectAllBtn) return;
        
        // 获取页面上的总影片数量
        const totalMovies = document.querySelectorAll('.batch-select-checkbox').length;
        const isAllSelected = this.selectedCount === totalMovies && totalMovies > 0;
        
        this.selectAllBtn.textContent = isAllSelected ? '取消全选' : '全选';
    }
    
    /**
     * 处理全选操作
     */
    handleSelectAll() {
        if (!window.favoriteManager) return;
        
        const totalMovies = document.querySelectorAll('.batch-select-checkbox').length;
        const isAllSelected = this.selectedCount === totalMovies && totalMovies > 0;
        
        // 切换全选状态
        window.favoriteManager.toggleSelectAll(!isAllSelected);
    }
    
    /**
     * 处理清空选择
     */
    handleClearSelection() {
        if (!window.favoriteManager) return;
        
        window.favoriteManager.clearSelection();
    }
    
    /**
     * 处理批量收藏
     */
    async handleBatchFavorite() {
        if (!window.favoriteManager || this.selectedCount === 0) return;
        
        try {
            // 显示加载状态
            this.setBatchFavoriteLoading(true);
            
            // 执行批量收藏
            await window.favoriteManager.batchAddFavorites();
            
        } catch (error) {
            console.error('批量收藏失败:', error);
        } finally {
            // 恢复按钮状态
            this.setBatchFavoriteLoading(false);
        }
    }
    
    /**
     * 处理关闭批量模式
     */
    handleCloseBatchMode() {
        if (!window.favoriteManager) return;
        
        window.favoriteManager.toggleBatchMode(false);
    }
    
    /**
     * 设置批量收藏按钮加载状态
     * @param {boolean} loading - 是否加载中
     */
    setBatchFavoriteLoading(loading) {
        if (!this.batchFavoriteBtn) return;
        
        if (loading) {
            this.batchFavoriteBtn.disabled = true;
            this.batchFavoriteBtn.innerHTML = `
                <span class="loading loading-spinner loading-sm"></span>
                批量收藏中...
            `;
        } else {
            this.batchFavoriteBtn.disabled = this.selectedCount === 0;
            this.batchFavoriteBtn.innerHTML = `
                <i class="bi bi-heart-fill"></i>
                批量收藏
            `;
        }
    }
    
    /**
     * 显示工具栏
     */
    show() {
        if (this.toolbar) {
            this.toolbar.classList.remove('hidden');
            this.isVisible = true;

            // 添加动画效果
            setTimeout(() => {
                this.toolbar.style.transform = 'translateY(0)';
            }, 10);
        }
    }
    
    /**
     * 隐藏工具栏
     */
    hide() {
        if (this.toolbar) {
            this.toolbar.classList.add('hidden');
            this.isVisible = false;
            this.toolbar.style.transform = 'translateY(100%)';
        }
    }
    
    /**
     * 销毁工具栏
     */
    destroy() {
        if (this.toolbar) {
            this.toolbar.remove();
        }
    }
    
    /**
     * 是否可见
     * @returns {boolean} 是否可见
     */
    isShown() {
        return this.isVisible;
    }

    /**
     * 更新批量操作按钮显示
     * @param {number[]} selectedMovies - 选中的影片ID列表
     */
    async updateBatchButtons(selectedMovies) {
        if (!window.favoriteManager || selectedMovies.length === 0) {
            // 没有选中影片时隐藏所有批量操作按钮
            this.batchFavoriteBtn?.classList.add('hidden');
            this.batchRemoveFavoriteBtn?.classList.add('hidden');
            this.batchEditBtn?.classList.add('hidden');
            this.batchFavoriteBtnMobile?.classList.add('hidden');
            this.batchRemoveFavoriteBtnMobile?.classList.add('hidden');
            this.batchEditBtnMobile?.classList.add('hidden');
            return;
        }

        // 判断当前是否在收藏页面
        const isFavoritesPage = window.location.pathname === '/favorites';

        if (isFavoritesPage) {
            // 在收藏页面，只显示“批量移除收藏”按钮
            this.batchFavoriteBtn?.classList.add('hidden');
            this.batchRemoveFavoriteBtn?.classList.remove('hidden');
            this.batchRemoveFavoriteBtn.disabled = false;
            this.batchEditBtn?.classList.remove('hidden');
            this.batchEditBtn.disabled = false;

            this.batchFavoriteBtnMobile?.classList.add('hidden');
            this.batchRemoveFavoriteBtnMobile?.classList.remove('hidden');
            this.batchRemoveFavoriteBtnMobile.disabled = false;
            this.batchEditBtnMobile?.classList.remove('hidden');
            this.batchEditBtnMobile.disabled = false;
            return;
        }

        try {
            // 获取选中影片的收藏状态
            const favoriteStates = await window.favoriteManager.batchGetFavoriteStatus(selectedMovies);

            let favoritedCount = 0;
            let unfavoritedCount = 0;

            favoriteStates.forEach((status) => {
                if (status.is_favorited) {
                    favoritedCount++;
                } else {
                    unfavoritedCount++;
                }
            });

            // 根据收藏状态显示相应的按钮
            if (favoritedCount === 0) {
                // 全部未收藏：只显示批量收藏按钮
                this.batchFavoriteBtn?.classList.remove('hidden');
                this.batchRemoveFavoriteBtn?.classList.add('hidden');
                this.batchFavoriteBtn.disabled = false;

                this.batchFavoriteBtnMobile?.classList.remove('hidden');
                this.batchRemoveFavoriteBtnMobile?.classList.add('hidden');
                this.batchFavoriteBtnMobile.disabled = false;
            } else if (unfavoritedCount === 0) {
                // 全部已收藏：只显示批量移除收藏按钮
                this.batchFavoriteBtn?.classList.add('hidden');
                this.batchRemoveFavoriteBtn?.classList.remove('hidden');
                this.batchRemoveFavoriteBtn.disabled = false;

                this.batchFavoriteBtnMobile?.classList.add('hidden');
                this.batchRemoveFavoriteBtnMobile?.classList.remove('hidden');
                this.batchRemoveFavoriteBtnMobile.disabled = false;
            } else {
                // 混合状态：显示两个按钮
                this.batchFavoriteBtn?.classList.remove('hidden');
                this.batchRemoveFavoriteBtn?.classList.remove('hidden');
                this.batchFavoriteBtn.disabled = false;
                this.batchRemoveFavoriteBtn.disabled = false;

                this.batchFavoriteBtnMobile?.classList.remove('hidden');
                this.batchRemoveFavoriteBtnMobile?.classList.remove('hidden');
                this.batchFavoriteBtnMobile.disabled = false;
                this.batchRemoveFavoriteBtnMobile.disabled = false;
            }

        } catch (error) {
            console.error('更新批量按钮时发生错误:', error);
            // 出错时显示批量收藏按钮作为默认选项
            this.batchFavoriteBtn?.classList.remove('hidden');
            this.batchRemoveFavoriteBtn?.classList.add('hidden');
            this.batchFavoriteBtn.disabled = false;

            this.batchFavoriteBtnMobile?.classList.remove('hidden');
            this.batchRemoveFavoriteBtnMobile?.classList.add('hidden');
            this.batchFavoriteBtnMobile.disabled = false;
        }
    }

    /**
     * 处理批量移除收藏
     */
    async handleBatchRemoveFavorite() {
        if (!window.favoriteManager || this.selectedCount === 0) return;

        try {
            // 显示加载状态
            this.setBatchRemoveFavoriteLoading(true);

            // 执行批量移除收藏
            await window.favoriteManager.batchRemoveFavorites();

        } catch (error) {
            console.error('批量移除收藏失败:', error);
        } finally {
            // 恢复按钮状态
            this.setBatchRemoveFavoriteLoading(false);
        }
    }

    /**
     * 设置批量移除收藏按钮加载状态
     * @param {boolean} loading - 是否加载中
     */
    setBatchRemoveFavoriteLoading(loading) {
        if (loading) {
            // 桌面端按钮
            if (this.batchRemoveFavoriteBtn) {
                this.batchRemoveFavoriteBtn.disabled = true;
                this.batchRemoveFavoriteBtn.innerHTML = `
                    <span class="loading loading-spinner loading-sm"></span>
                    移除收藏中...
                `;
            }

            // 移动端按钮
            if (this.batchRemoveFavoriteBtnMobile) {
                this.batchRemoveFavoriteBtnMobile.disabled = true;
                this.batchRemoveFavoriteBtnMobile.innerHTML = `
                    <span class="loading loading-spinner loading-xs"></span>
                    移除中...
                `;
            }
        } else {
            // 桌面端按钮
            if (this.batchRemoveFavoriteBtn) {
                this.batchRemoveFavoriteBtn.disabled = this.selectedCount === 0;
                this.batchRemoveFavoriteBtn.innerHTML = `
                    <i class="bi bi-heart-slash"></i>
                    批量移除收藏
                `;
            }

            // 移动端按钮
            if (this.batchRemoveFavoriteBtnMobile) {
                this.batchRemoveFavoriteBtnMobile.disabled = this.selectedCount === 0;
                this.batchRemoveFavoriteBtnMobile.innerHTML = `
                    <i class="bi bi-heart-slash"></i>
                    移除
                `;
            }
        }
    }
}

// 导出批量工具栏类
window.BatchToolbar = BatchToolbar;
