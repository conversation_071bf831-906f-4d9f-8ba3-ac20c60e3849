"""
收藏功能 API 路由
提供影片收藏的增删查改操作接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from app.core.database import get_db
from app.services.favorite_service import FavoriteService
from app.services.movie_service import MovieService
from app.schemas.schemas import (
    BaseResponse, FavoriteResponse, FavoriteStatusResponse,
    BatchFavoriteRequest, BatchFavoriteResponse, FavoriteListResponse,
    MovieListItem, FavoriteFilterParams
)
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/favorites",
    tags=["收藏功能"],
    responses={404: {"description": "资源不存在"}}
)


@router.post("/batch", response_model=BaseResponse)
async def batch_add_favorites(request: BatchFavoriteRequest, db: Session = Depends(get_db)):
    """
    批量收藏影片

    Args:
        request: 批量收藏请求

    Returns:
        批量收藏结果
    """
    try:
        # 参数验证
        if not request.movie_ids:
            raise HTTPException(status_code=400, detail="影片ID列表不能为空")

        if len(request.movie_ids) > 100:
            raise HTTPException(status_code=400, detail="单次最多只能收藏100个影片")

        favorite_service = FavoriteService(db)
        result = favorite_service.batch_add_favorites(request.movie_ids)

        response_data = BatchFavoriteResponse(
            success_count=result["success_count"],
            failed_count=result["failed_count"],
            success_movie_ids=result["success_movie_ids"],
            failed_movie_ids=result["failed_movie_ids"],
            errors=result["errors"]
        )

        # 构建消息
        if result["failed_count"] == 0:
            message = f"成功收藏 {result['success_count']} 个影片"
        elif result["success_count"] == 0:
            message = f"收藏失败，{result['failed_count']} 个影片无法收藏"
        else:
            message = f"成功收藏 {result['success_count']} 个影片，{result['failed_count']} 个失败"

        return BaseResponse(
            success=True,
            message=message,
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量收藏时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量收藏失败: {str(e)}")


@router.delete("/batch", response_model=BaseResponse)
async def batch_remove_favorites(request: BatchFavoriteRequest, db: Session = Depends(get_db)):
    """
    批量移除收藏影片

    Args:
        request: 批量移除收藏请求

    Returns:
        批量移除收藏结果
    """
    try:
        # 参数验证
        if not request.movie_ids:
            raise HTTPException(status_code=400, detail="影片ID列表不能为空")

        if len(request.movie_ids) > 100:
            raise HTTPException(status_code=400, detail="单次最多只能操作100个影片")

        favorite_service = FavoriteService(db)
        result = favorite_service.batch_remove_favorites(request.movie_ids)

        response_data = BatchFavoriteResponse(
            success_count=result["success_count"],
            failed_count=result["failed_count"],
            success_movie_ids=result["success_movie_ids"],
            failed_movie_ids=result["failed_movie_ids"],
            errors=result["errors"]
        )

        # 构建消息
        if result["failed_count"] == 0:
            message = f"成功移除 {result['success_count']} 个影片的收藏"
        elif result["success_count"] == 0:
            message = f"移除收藏失败，{result['failed_count']} 个影片无法移除"
        else:
            message = f"成功移除 {result['success_count']} 个影片的收藏，{result['failed_count']} 个失败"

        return BaseResponse(
            success=True,
            message=message,
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量移除收藏时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量移除收藏失败: {str(e)}")


@router.post("/{movie_id}", response_model=BaseResponse)
async def add_favorite(movie_id: int, db: Session = Depends(get_db)):
    """
    收藏影片
    
    Args:
        movie_id: 影片ID
        
    Returns:
        收藏结果
    """
    try:
        favorite_service = FavoriteService(db)
        success, message, favorite = favorite_service.add_favorite(movie_id)
        
        if not success:
            if "已在收藏夹中" in message:
                raise HTTPException(status_code=409, detail=message)
            elif "不存在" in message:
                raise HTTPException(status_code=404, detail=message)
            else:
                raise HTTPException(status_code=400, detail=message)
        
        # 构建响应数据
        favorite_response = FavoriteResponse(
            id=favorite.id,
            movie_id=favorite.movie_id,
            created_at=favorite.created_at,
            updated_at=favorite.updated_at
        )
        
        return BaseResponse(
            success=True,
            message=message,
            data=favorite_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"收藏影片时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"收藏失败: {str(e)}")


@router.delete("/{movie_id}", response_model=BaseResponse)
async def remove_favorite(movie_id: int, db: Session = Depends(get_db)):
    """
    取消收藏影片
    
    Args:
        movie_id: 影片ID
        
    Returns:
        取消收藏结果
    """
    try:
        favorite_service = FavoriteService(db)
        success, message = favorite_service.remove_favorite(movie_id)
        
        if not success:
            if "未收藏" in message:
                raise HTTPException(status_code=404, detail=message)
            else:
                raise HTTPException(status_code=400, detail=message)
        
        return BaseResponse(
            success=True,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消收藏时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"取消收藏失败: {str(e)}")


@router.put("/{movie_id}/toggle", response_model=BaseResponse)
async def toggle_favorite(movie_id: int, db: Session = Depends(get_db)):
    """
    切换影片收藏状态
    
    Args:
        movie_id: 影片ID
        
    Returns:
        切换结果
    """
    try:
        favorite_service = FavoriteService(db)
        success, message, is_favorited, favorite = favorite_service.toggle_favorite(movie_id)
        
        if not success:
            if "不存在" in message:
                raise HTTPException(status_code=404, detail=message)
            else:
                raise HTTPException(status_code=400, detail=message)
        
        # 构建响应数据
        data = {
            "movie_id": movie_id,
            "is_favorited": is_favorited,
            "message": message
        }
        
        if favorite:
            data["favorite"] = FavoriteResponse(
                id=favorite.id,
                movie_id=favorite.movie_id,
                created_at=favorite.created_at,
                updated_at=favorite.updated_at
            )
        
        return BaseResponse(
            success=True,
            message=message,
            data=data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换收藏状态时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")



@router.get("/{movie_id}/status", response_model=BaseResponse)
async def get_favorite_status(movie_id: int, db: Session = Depends(get_db)):
    """
    获取影片收藏状态
    
    Args:
        movie_id: 影片ID
        
    Returns:
        收藏状态信息
    """
    try:
        favorite_service = FavoriteService(db)
        status = favorite_service.get_favorite_status(movie_id)
        
        status_response = FavoriteStatusResponse(
            movie_id=status["movie_id"],
            is_favorited=status["is_favorited"],
            favorite_id=status["favorite_id"],
            favorited_at=status["favorited_at"]
        )
        
        return BaseResponse(
            success=True,
            message="获取收藏状态成功",
            data=status_response
        )
        
    except Exception as e:
        logger.error(f"获取收藏状态时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取收藏状态失败: {str(e)}")


@router.post("", response_model=FavoriteListResponse)
async def get_favorites(
    filter_params: FavoriteFilterParams,
    db: Session = Depends(get_db)
):
    """
    获取收藏列表

    支持多种过滤和搜索条件：
    - 按标题、原始标题、剧情、演员名称搜索
    - 按分类、系列、标签多选过滤
    - 按年份、评分范围过滤
    - 分页显示和排序

    Args:
        filter_params: 过滤参数（JSON格式）

    Returns:
        收藏列表，包含基本信息和关联数据统计
    """
    try:
        favorite_service = FavoriteService(db)
        movie_service = MovieService(db)

        favorites, total_count = favorite_service.get_favorites(filter_params)

        # 构建响应数据
        favorite_responses = []
        for favorite in favorites:
            # 获取影片信息和图片UUID
            movie_info = movie_service.get_movie_with_image_uuids(favorite.movie)

            movie_item = MovieListItem(
                id=favorite.movie.id,
                title=favorite.movie.title,
                year=favorite.movie.year,
                rating=favorite.movie.rating,
                runtime=favorite.movie.runtime,
                poster_uuid=movie_info["poster_uuid"],
                fanart_uuid=movie_info["fanart_uuid"],
                thumb_uuid=movie_info["thumb_uuid"],
                series_name=favorite.movie.series.name if favorite.movie.series else None,
                genre_count=len(favorite.movie.genres),
                tag_count=len(favorite.movie.tags),
                actor_count=len(favorite.movie.actors)
            )

            favorite_response = FavoriteResponse(
                id=favorite.id,
                movie_id=favorite.movie_id,
                created_at=favorite.created_at,
                updated_at=favorite.updated_at,
                movie=movie_item
            )
            favorite_responses.append(favorite_response)

        return FavoriteListResponse(
            success=True,
            message=f"获取收藏列表成功，共 {len(favorite_responses)} 个收藏",
            data=favorite_responses,
            total_count=total_count,
            limit=filter_params.limit,
            offset=filter_params.offset
        )

    except Exception as e:
        logger.error(f"获取收藏列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取收藏列表失败: {str(e)}")
