/**
 * 通用数据管理器
 * 处理 genres、tags、actors、series 等模块的增删改查功能
 */
class DataManager {
    constructor(config) {
        // 基础配置
        this.config = config;
        this.type = config.type; // 'genres', 'tags', 'actors', 'series'
        this.apiEndpoint = config.apiEndpoint;
        this.pageTitle = config.pageTitle;
        
        // 数据状态
        this.data = [];
        this.selectedItems = new Set();
        this.currentItem = null;
        this.searchTimeout = null;
        this.isLoading = false;
        this.currentPage = 1;
        this.pageSize = 20;
        this.searchQuery = '';
        
        // DOM 元素 ID 前缀
        this.prefix = config.prefix;
        
        // 字段配置
        this.fields = config.fields;
        this.tableColumns = config.tableColumns;
        this.formFields = config.formFields;
    }

    /**
     * 初始化数据管理器
     */
    async init() {
        // 检查是否在正确的页面上
        if (!this.isOnCorrectPage()) {
            return;
        }

        this.bindEvents();
        await this.loadData();
    }

    /**
     * 检查是否在正确的管理页面
     */
    isOnCorrectPage() {
        return document.getElementById(`${this.prefix}-table-body`) !== null;
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 添加按钮
        document.getElementById(`add-${this.prefix}-btn`)?.addEventListener('click', () => {
            this.showModal();
        });

        document.getElementById(`add-first-${this.prefix}-btn`)?.addEventListener('click', () => {
            this.showModal();
        });

        // 搜索输入框
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        // 批量删除按钮
        document.getElementById('batch-delete-btn')?.addEventListener('click', () => {
            this.showBatchDeleteModal();
        });

        // 刷新按钮
        document.getElementById('refresh-btn')?.addEventListener('click', () => {
            this.loadData();
        });

        // 全选复选框
        document.getElementById('table-select-all')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 表单提交
        document.getElementById(`${this.prefix}-form`)?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitForm();
        });

        // 删除确认
        document.getElementById(`confirm-delete-${this.prefix}-btn`)?.addEventListener('click', () => {
            this.confirmDelete();
        });

        // 批量删除确认
        document.getElementById('confirm-batch-delete-btn')?.addEventListener('click', () => {
            this.confirmBatchDelete();
        });


    }

    /**
     * 加载数据
     */
    async loadData() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading();

            const response = await api.get(this.apiEndpoint);
            if (response.success) {
                this.data = response.data || [];
                this.updateDataDisplay();
            } else {
                throw new Error(response.message || '加载失败');
            }
        } catch (error) {
            console.error(`Failed to load ${this.type}:`, error);
            toast.error(`加载${this.pageTitle}失败: ${error.message}`);
            this.showEmptyState();
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    /**
     * 更新数据显示（包含分页）
     */
    updateDataDisplay(dataToShow = null) {
        const items = dataToShow || this.data;
        const totalItems = items.length;
        const totalPages = Math.ceil(totalItems / this.pageSize);
        
        // 计算当前页的数据
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const currentPageItems = items.slice(startIndex, endIndex);
        
        this.renderData(currentPageItems);
        this.updatePagination(totalPages, totalItems);
        this.updateStatistics();
        this.bindRowEvents();

        // 显示或隐藏空状态
        if (items.length === 0) {
            this.showEmptyState();
        } else {
            this.hideEmptyState();
        }
    }

    /**
     * 渲染数据列表
     */
    renderData(itemsToRender = null) {
        const tableBody = document.getElementById(`${this.prefix}-table-body`);
        if (!tableBody) return;

        const items = itemsToRender || this.data;

        if (items.length === 0) {
            const colCount = this.tableColumns.length + 2; // +2 for checkbox and actions
            tableBody.innerHTML = `<tr><td colspan="${colCount}" class="text-center text-base-content/60">暂无${this.pageTitle.slice(0, -2)}数据</td></tr>`;
            return;
        }

        tableBody.innerHTML = items.map(item => this.createTableRow(item)).join('');
    }

    /**
     * 创建表格行
     */
    createTableRow(item) {
        const isSelected = this.selectedItems.has(item.id); // 移除末尾的 's'
        
        let columnsHTML = '';
        this.tableColumns.forEach(column => {
            let value = item[column.field];
            
            // 根据列类型格式化值
            if (column.type === 'badge') {
                value = `<div class="badge badge-primary">${value || 0}</div>`;
            } else if (column.type === 'text') {
                const maxLength = column.maxLength || 50;
                const displayValue = value || column.defaultValue || '';
                if (displayValue.length > maxLength) {
                    value = `<div class="max-w-xs truncate" title="${escapeHtml(displayValue)}">${escapeHtml(displayValue)}</div>`;
                } else {
                    value = `<div>${escapeHtml(displayValue)}</div>`;
                }
            } else if (column.type === 'datetime') {
                const formattedDate = value ? this.formatDateTime(value) : '-';
                value = `<div class="text-sm text-base-content/70">${formattedDate}</div>`;
            } else {
                value = `<div class="font-medium">${escapeHtml(value || '')}</div>`;
            }
            
            columnsHTML += `<td>${value}</td>`;
        });

        return `
            <tr class="hover">
                <td>
                    <label>
                        <input type="checkbox" class="checkbox checkbox-sm ${this.prefix}-checkbox"
                               data-${this.prefix}-id="${item.id}"
                               ${isSelected ? 'checked' : ''}>
                    </label>
                </td>
                ${columnsHTML}
                <td>
                    <div class="flex gap-1">
                        <button type="button" class="btn btn-xs btn-primary edit-${this.prefix}-btn"
                                data-${this.prefix}-id="${item.id}" title="编辑">
                            <i class="bi bi-pencil" style="font-size: 0.75rem;" aria-label="编辑图标"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-error delete-${this.prefix}-btn"
                                data-${this.prefix}-id="${item.id}" title="删除">
                            <i class="bi bi-trash" style="font-size: 0.75rem;" aria-label="删除图标"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 绑定行事件
     */
    bindRowEvents() {
        // 绑定复选框事件
        const checkboxes = document.querySelectorAll(`.${this.prefix}-checkbox`);
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const itemId = parseInt(e.target.dataset[`${this.prefix}Id`]);
                if (e.target.checked) {
                    this.selectedItems.add(itemId);
                } else {
                    this.selectedItems.delete(itemId);
                }
                this.updateBatchButtons();
            });
        });

        // 绑定编辑按钮事件
        const editButtons = document.querySelectorAll(`.edit-${this.prefix}-btn`);
        editButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const itemId = parseInt(e.currentTarget.dataset[`${this.prefix}Id`]);
                this.editItem(itemId);
            });
        });

        // 绑定删除按钮事件
        const deleteButtons = document.querySelectorAll(`.delete-${this.prefix}-btn`);
        deleteButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const itemId = parseInt(e.currentTarget.dataset[`${this.prefix}Id`]);
                this.deleteItem(itemId);
            });
        });
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchButtons() {
        const hasSelection = this.selectedItems.size > 0;
        const batchBtn = document.getElementById('batch-delete-btn');
        const selectedCount = document.getElementById(`selected-${this.prefix}`);

        if (batchBtn) {
            if (hasSelection) {
                batchBtn.classList.remove('hidden');
                batchBtn.disabled = false;
            } else {
                batchBtn.classList.add('hidden');
            }
        }

        if (selectedCount) {
            selectedCount.textContent = this.selectedItems.size;
        }

        // 更新全选复选框状态
        this.updateSelectAllCheckbox();
    }

    /**
     * 更新全选复选框状态
     */
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('table-select-all');
        if (!selectAllCheckbox) return;

        const currentPageCheckboxes = document.querySelectorAll(`.${this.prefix}-checkbox`);

        if (currentPageCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
            return;
        }

        const checkedCount = Array.from(currentPageCheckboxes).filter(cb => cb.checked).length;

        if (checkedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === currentPageCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 处理搜索
     */
    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.searchQuery = query.trim();
            this.currentPage = 1; // 重置到第一页

            if (this.searchQuery === '') {
                this.updateDataDisplay();
            } else {
                const filtered = this.data.filter(item => {
                    return this.config.searchFields.some(field => {
                        const value = item[field];
                        return value && value.toLowerCase().includes(this.searchQuery.toLowerCase());
                    });
                });
                this.updateDataDisplay(filtered);
            }
        }, 300);
    }

    /**
     * 切换全选
     */
    toggleSelectAll(checked) {
        // 获取当前页面上显示的复选框
        const checkboxes = document.querySelectorAll(`.${this.prefix}-checkbox`);

        checkboxes.forEach(checkbox => {
            const itemId = parseInt(checkbox.dataset[`${this.prefix}Id`]);
            checkbox.checked = checked;

            if (checked) {
                this.selectedItems.add(itemId);
            } else {
                this.selectedItems.delete(itemId);
            }
        });

        this.updateBatchButtons();
    }



    /**
     * 更新统计信息
     */
    updateStatistics() {
        // 计算统计数据
        const totalCount = this.data.length;
        const usedCount = this.data.filter(item => item.movie_count > 0).length;
        const unusedCount = this.data.filter(item => item.movie_count === 0).length;

        // 更新统计卡片中的总数量
        const totalElement = document.getElementById(`total-${this.prefix}`);
        if (totalElement) {
            totalElement.textContent = totalCount;
        }

        // 更新已使用项目数量
        const usedElement = document.getElementById(`used-${this.prefix}`);
        if (usedElement) {
            usedElement.textContent = usedCount;
        }

        // 更新未使用项目数量
        const unusedElement = document.getElementById(`unused-${this.prefix}`);
        if (unusedElement) {
            unusedElement.textContent = unusedCount;
        }

        // 更新页面底部的计数信息
        const countInfoElement = document.getElementById(`${this.prefix}-count-info`);
        if (countInfoElement) {
            const itemName = this.pageTitle.slice(0, -2); // 移除"管理"
            countInfoElement.textContent = `共 ${totalCount} 个${itemName}`;
        }


    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
        if (!dateString) return '-';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '-';

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}`;
        } catch (error) {
            console.error('Error formatting date:', error);
            return '-';
        }
    }

    /**
     * 显示模态框
     */
    showModal(item = null) {
        this.currentItem = item;
        const modal = document.getElementById(`${this.prefix}-modal`);

        // 尝试多种可能的标题元素 ID
        const title = document.getElementById('modal-title') ||
                     document.getElementById(`${this.prefix}-modal-title`);

        // 尝试多种可能的提交按钮文本元素 ID
        const submitText = document.getElementById('submit-text') ||
                          document.getElementById(`${this.prefix}-submit-text`);

        if (title) {
            if (item) {
                title.textContent = `编辑${this.pageTitle.slice(0, -2)}`;
            } else {
                title.textContent = `添加${this.pageTitle.slice(0, -2)}`;
            }
        }

        if (submitText) {
            if (item) {
                submitText.textContent = `更新${this.pageTitle.slice(0, -2)}`;
            } else {
                submitText.textContent = `添加${this.pageTitle.slice(0, -2)}`;
            }
        }

        if (item) {
            this.fillForm(item);
        } else {
            this.clearForm();
        }

        if (modal) {
            modal.showModal();
        }
    }

    /**
     * 填充表单
     */
    fillForm(item) {
        // 设置隐藏的 ID 字段
        const idField = document.getElementById(`${this.prefix}-id`);
        if (idField) {
            idField.value = item.id;
        }

        this.formFields.forEach(field => {
            const element = document.getElementById(`${this.prefix}-${field.name}`);
            if (element) {
                element.value = item[field.name] || '';
            }
        });
    }

    /**
     * 清空表单
     */
    clearForm() {
        const form = document.getElementById(`${this.prefix}-form`);
        if (form) {
            form.reset();
        }

        const idField = document.getElementById(`${this.prefix}-id`);
        if (idField) {
            idField.value = '';
        }

        this.clearFormErrors();
    }

    /**
     * 清除表单错误
     */
    clearFormErrors() {
        this.formFields.forEach(field => {
            // 尝试多种可能的错误元素 ID
            const errorElement = document.getElementById(`${field.name}-error`) ||
                                document.getElementById(`${this.prefix}-${field.name}-error`);
            if (errorElement) {
                errorElement.classList.add('hidden');
            }
        });
    }

    /**
     * 提交表单
     */
    async submitForm() {
        // 尝试多种可能的提交按钮 ID
        const submitBtn = document.getElementById('submit-btn') ||
                         document.getElementById(`${this.prefix}-submit-btn`);

        // 尝试多种可能的加载元素
        const submitLoading = document.getElementById('submit-loading') ||
                             document.getElementById(`${this.prefix}-submit-loading`) ||
                             submitBtn?.querySelector('.loading');

        // 尝试多种可能的提交文本元素
        const submitText = document.getElementById('submit-text') ||
                          document.getElementById(`${this.prefix}-submit-text`) ||
                          submitBtn;

        // 获取表单数据
        const formData = {};
        this.formFields.forEach(field => {
            const element = document.getElementById(`${this.prefix}-${field.name}`);
            if (element) {
                formData[field.name] = element.value.trim();
            }
        });

        // 验证表单
        if (!this.validateForm(formData)) {
            return;
        }

        // 显示加载状态
        if (submitBtn) {
            submitBtn.disabled = true;
        }

        if (submitLoading) {
            submitLoading.classList.remove('hidden');
        }

        const originalText = submitBtn ? submitBtn.textContent : '';
        if (submitText) {
            if (submitText === submitBtn) {
                // 如果 submitText 就是按钮本身，保存原始文本并更新
                submitText.textContent = this.currentItem ? '更新中...' : '添加中...';
            } else {
                submitText.textContent = this.currentItem ? '更新中...' : '添加中...';
            }
        }

        try {
            let response;
            if (this.currentItem) {
                response = await api.put(`${this.apiEndpoint}/${this.currentItem.id}`, formData);
            } else {
                response = await api.post(this.apiEndpoint, formData);
            }

            if (response.success) {
                toast.success(this.currentItem ? `${this.pageTitle.slice(0, -2)}更新成功` : `${this.pageTitle.slice(0, -2)}添加成功`);
                document.getElementById(`${this.prefix}-modal`).close();
                await this.loadData();
            } else {
                throw new Error(response.message || '操作失败');
            }
        } catch (error) {
            console.error(`Failed to submit ${this.type} form:`, error);
            toast.error('操作失败: ' + error.message);
        } finally {
            // 恢复按钮状态
            if (submitBtn) {
                submitBtn.disabled = false;
            }

            if (submitLoading) {
                submitLoading.classList.add('hidden');
            }

            if (submitText) {
                if (submitText === submitBtn) {
                    submitText.textContent = originalText || (this.currentItem ? `更新${this.pageTitle.slice(0, -2)}` : `添加${this.pageTitle.slice(0, -2)}`);
                } else {
                    submitText.textContent = this.currentItem ? `更新${this.pageTitle.slice(0, -2)}` : `添加${this.pageTitle.slice(0, -2)}`;
                }
            }
        }
    }

    /**
     * 验证表单
     */
    validateForm(formData) {
        this.clearFormErrors();
        let isValid = true;

        this.formFields.forEach(field => {
            if (field.required && !formData[field.name]) {
                // 尝试多种可能的错误元素 ID
                const errorElement = document.getElementById(`${field.name}-error`) ||
                                    document.getElementById(`${this.prefix}-${field.name}-error`);
                if (errorElement) {
                    errorElement.textContent = field.errorMessage || `请输入${field.label}`;
                    errorElement.classList.remove('hidden');
                }
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * 编辑项目
     */
    editItem(id) {
        const item = this.data.find(item => item.id === id);
        if (item) {
            this.showModal(item);
        }
    }

    /**
     * 删除项目
     */
    deleteItem(id) {
        const item = this.data.find(item => item.id === id);
        if (item) {
            this.currentItem = item;
            const messageElement = document.getElementById(`delete-${this.prefix}-message`);
            if (messageElement) {
                messageElement.textContent = `您确定要删除${this.pageTitle.slice(0, -2)}"${item.name}"吗？`;
            }
            document.getElementById(`delete-${this.prefix}-modal`).showModal();
        }
    }

    /**
     * 确认删除
     */
    async confirmDelete() {
        try {
            if (this.currentItem) {
                const forceCheckbox = document.getElementById(`force-delete-${this.prefix}-checkbox`);
                const force = forceCheckbox ? forceCheckbox.checked : false;

                // 使用对应的 API 方法
                let response;
                switch (this.type) {
                    case 'genres':
                        response = await api.deleteGenre([this.currentItem.id], force);
                        break;
                    case 'tags':
                        response = await api.deleteTag([this.currentItem.id], force);
                        break;
                    case 'series':
                        response = await api.deleteSeries([this.currentItem.id], force);
                        break;
                    case 'actors':
                        response = await api.deleteActors([this.currentItem.id], force);
                        break;
                    default:
                        throw new Error(`Unknown type: ${this.type}`);
                }

                if (response.success) {
                    await this.loadData();
                    document.getElementById(`delete-${this.prefix}-modal`).close();
                    toast.success(`${this.pageTitle.slice(0, -2)}删除成功`);
                } else {
                    throw new Error(response.message || '删除失败');
                }
            }
        } catch (error) {
            console.error(`Failed to delete ${this.type}:`, error);
            toast.error(`删除${this.pageTitle.slice(0, -2)}失败: ${error.message}`);
        }
    }

    /**
     * 显示批量删除模态框
     */
    showBatchDeleteModal() {
        if (this.selectedItems.size === 0) {
            toast.warning(`请先选择要删除的${this.pageTitle}`);
            return;
        }

        const countElement = document.getElementById('delete-count');
        if (countElement) {
            countElement.textContent = this.selectedItems.size;
        }

        document.getElementById(`batch-delete-${this.prefix}-modal`).showModal();
    }

    /**
     * 确认批量删除
     */
    async confirmBatchDelete() {
        try {
            if (this.selectedItems.size > 0) {
                const forceCheckbox = document.getElementById(`force-batch-delete-${this.prefix}-checkbox`);
                const force = forceCheckbox ? forceCheckbox.checked : false;

                const itemIds = Array.from(this.selectedItems);

                // 使用对应的 API 方法
                let response;
                switch (this.type) {
                    case 'genres':
                        response = await api.deleteGenre(itemIds, force);
                        break;
                    case 'tags':
                        response = await api.deleteTag(itemIds, force);
                        break;
                    case 'series':
                        response = await api.deleteSeries(itemIds, force);
                        break;
                    case 'actors':
                        response = await api.deleteActors(itemIds, force);
                        break;
                    default:
                        throw new Error(`Unknown type: ${this.type}`);
                }

                this.selectedItems.clear();
                await this.loadData();
                this.updateBatchButtons();
                document.getElementById(`batch-delete-${this.prefix}-modal`).close();

                if (response.success) {
                    toast.success(`批量删除${this.pageTitle}成功`);

                    // 显示详细的删除结果
                    if (response.data && response.data.failed_count > 0) {
                        toast.warning(response.message);
                    }
                } else {
                    throw new Error(response.message || '批量删除失败');
                }
            }
        } catch (error) {
            console.error(`Failed to batch delete ${this.type}:`, error);
            toast.error(`批量删除${this.pageTitle}失败: ${error.message}`);
        }
    }

    /**
     * 更新分页组件
     */
    updatePagination(totalPages, totalItems) {
        const paginationContainer = document.getElementById('pagination-container');
        if (!paginationContainer) return;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        const managerName = `window.${this.type}Manager`;
        let paginationHTML = '<div class="join">';

        // 上一页按钮
        paginationHTML += `
            <button class="join-item btn btn-sm ${this.currentPage === 1 ? 'btn-disabled' : ''}"
                    onclick="${managerName}.goToPage(${this.currentPage - 1})"
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                «
            </button>
        `;

        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button class="join-item btn btn-sm" onclick="${managerName}.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<button class="join-item btn btn-sm btn-disabled">...</button>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="join-item btn btn-sm ${i === this.currentPage ? 'btn-active' : ''}"
                        onclick="${managerName}.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<button class="join-item btn btn-sm btn-disabled">...</button>`;
            }
            paginationHTML += `<button class="join-item btn btn-sm" onclick="${managerName}.goToPage(${totalPages})">${totalPages}</button>`;
        }

        // 下一页按钮
        paginationHTML += `
            <button class="join-item btn btn-sm ${this.currentPage === totalPages ? 'btn-disabled' : ''}"
                    onclick="${managerName}.goToPage(${this.currentPage + 1})"
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                »
            </button>
        `;

        paginationHTML += '</div>';

        // 添加页面信息
        paginationHTML += `
            <div class="text-sm text-base-content/60 mt-2">
                显示第 ${(this.currentPage - 1) * this.pageSize + 1} - ${Math.min(this.currentPage * this.pageSize, totalItems)} 条，共 ${totalItems} 条
            </div>
        `;

        paginationContainer.innerHTML = paginationHTML;
    }

    /**
     * 跳转到指定页面
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.data.length / this.pageSize);
        if (page < 1 || page > totalPages) return;

        this.currentPage = page;
        this.updateDataDisplay();
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loadingState = document.getElementById('loading-state');
        const tableView = document.getElementById('table-view');
        const emptyState = document.getElementById('empty-state');

        if (loadingState) {
            loadingState.classList.remove('hidden');
            loadingState.classList.add('flex');
        }
        if (tableView) {
            tableView.classList.add('hidden');
        }
        if (emptyState) {
            emptyState.classList.add('hidden');
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingState = document.getElementById('loading-state');
        const tableView = document.getElementById('table-view');

        if (loadingState) {
            loadingState.classList.add('hidden');
            loadingState.classList.remove('flex');
        }
        if (tableView) {
            tableView.classList.remove('hidden');
        }
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const emptyState = document.getElementById('empty-state');
        const tableView = document.getElementById('table-view');
        const loadingState = document.getElementById('loading-state');

        if (emptyState) {
            emptyState.classList.remove('hidden');
        }
        if (tableView) {
            tableView.classList.add('hidden');
        }
        if (loadingState) {
            loadingState.classList.add('hidden');
            loadingState.classList.remove('flex');
        }
    }

    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        const emptyState = document.getElementById('empty-state');
        if (emptyState) {
            emptyState.classList.add('hidden');
        }
    }
}

// 配置对象定义
const DATA_CONFIGS = {
    genres: {
        type: 'genres',
        apiEndpoint: '/genres',
        pageTitle: '分类管理',
        prefix: 'genres',
        searchFields: ['name', 'description'],
        tableColumns: [
            { field: 'name', label: '分类名称', type: 'name' },
            { field: 'description', label: '描述', type: 'text', maxLength: 50, defaultValue: '无描述' },
            { field: 'movie_count', label: '影片数量', type: 'badge' },
            { field: 'created_at', label: '创建时间', type: 'datetime' }
        ],
        formFields: [
            { name: 'name', label: '分类名称', required: true, errorMessage: '请输入分类名称' },
            { name: 'description', label: '描述', required: false }
        ]
    },
    tags: {
        type: 'tags',
        apiEndpoint: '/tags',
        pageTitle: '标签管理',
        prefix: 'tags',
        searchFields: ['name', 'description'],
        tableColumns: [
            { field: 'name', label: '标签名称', type: 'name' },
            { field: 'description', label: '描述', type: 'text', maxLength: 50, defaultValue: '无描述' },
            { field: 'movie_count', label: '影片数量', type: 'badge' },
            { field: 'created_at', label: '创建时间', type: 'datetime' }
        ],
        formFields: [
            { name: 'name', label: '标签名称', required: true, errorMessage: '请输入标签名称' },
            { name: 'description', label: '描述', required: false }
        ]
    },
    series: {
        type: 'series',
        apiEndpoint: '/series',
        pageTitle: '系列管理',
        prefix: 'series',
        searchFields: ['name', 'description'],
        tableColumns: [
            { field: 'name', label: '系列名称', type: 'name' },
            { field: 'description', label: '描述', type: 'text', maxLength: 50, defaultValue: '无描述' },
            { field: 'movie_count', label: '影片数量', type: 'badge' },
            { field: 'created_at', label: '创建时间', type: 'datetime' }
        ],
        formFields: [
            { name: 'name', label: '系列名称', required: true, errorMessage: '请输入系列名称' },
            { name: 'description', label: '描述', required: false }
        ]
    },
    actors: {
        type: 'actors',
        apiEndpoint: '/actors',
        pageTitle: '演员管理',
        prefix: 'actors',
        searchFields: ['name', 'biography'],
        tableColumns: [
            { field: 'name', label: '演员姓名', type: 'name' },
            { field: 'biography', label: '传记', type: 'text', maxLength: 50, defaultValue: '暂无传记' },
            { field: 'movie_count', label: '影片数量', type: 'badge' },
            { field: 'created_at', label: '创建时间', type: 'datetime' }
        ],
        formFields: [
            { name: 'name', label: '演员姓名', required: true, errorMessage: '请输入演员姓名' },
            { name: 'biography', label: '传记', required: false }
        ]
    }
};

// 页面加载完成后初始化对应的管理器
document.addEventListener('DOMContentLoaded', async () => {
    const path = window.location.pathname;

    if (path === '/genres') {
        window.genresManager = new DataManager(DATA_CONFIGS.genres);
        await window.genresManager.init();
    } else if (path === '/tags') {
        window.tagsManager = new DataManager(DATA_CONFIGS.tags);
        await window.tagsManager.init();
    } else if (path === '/series') {
        window.seriesManager = new DataManager(DATA_CONFIGS.series);
        await window.seriesManager.init();
    } else if (path === '/actors') {
        window.actorsManager = new DataManager(DATA_CONFIGS.actors);
        await window.actorsManager.init();
    }
});
