/* 管理页面统一样式 */

/* 页面头部样式 */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.page-header .text-muted {
    font-size: 0.875rem;
}

/* 搜索和操作区域 */
.search-actions-row {
    margin-bottom: 1.5rem;
}

.search-input-group {
    max-width: 400px;
}

.search-input-group .input-group-text {
    background-color: var(--tblr-bg-surface);
    border-color: var(--tblr-border-color);
}

.action-buttons {
    gap: 0.5rem;
}

/* 表格样式 */
.manager-table {
    margin-bottom: 0;
}

.manager-table th {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--tblr-text-muted);
    border-bottom: 2px solid var(--tblr-border-color);
    padding: 0.75rem;
}

.manager-table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--tblr-border-color-translucent);
}

.manager-table .w-1 {
    width: 1%;
    white-space: nowrap;
}

/* 操作按钮样式 */
.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    margin-right: 0.25rem;
}

.action-btn:last-child {
    margin-right: 0;
}

/* 表格操作按钮 */
.table-actions {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.table-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1;
    border-radius: 0.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.table-actions .btn .icon {
    width: 16px;
    height: 16px;
}

.table-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 数据展示格式统一 */
.manager-table td strong {
    font-weight: 600;
    color: var(--tblr-text);
}

.manager-table .badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

.manager-table .text-muted {
    color: var(--tblr-text-muted) !important;
    font-size: 0.875rem;
}

.manager-table small.text-muted {
    font-size: 0.75rem;
}

/* 行选中状态 */
.manager-table tbody tr:hover {
    background-color: var(--tblr-bg-surface-secondary);
    transition: background-color 0.15s ease-in-out;
}

.manager-table tbody tr.selected {
    background-color: rgba(var(--tblr-primary-rgb), 0.1);
}

.manager-table tbody tr.selected:hover {
    background-color: rgba(var(--tblr-primary-rgb), 0.15);
}

/* 复选框样式 */
.manager-table .form-check-input {
    margin: 0;
    cursor: pointer;
}

.manager-table .form-check-input:checked {
    background-color: var(--tblr-primary);
    border-color: var(--tblr-primary);
}

/* 状态显示样式 */
.loading-state {
    padding: 3rem 0;
    text-align: center;
}

.loading-state .spinner-border {
    width: 2rem;
    height: 2rem;
    color: var(--tblr-primary);
}

.loading-state .text-muted {
    margin-top: 0.75rem;
    font-size: 0.875rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background-color: var(--tblr-bg-surface);
    border-radius: 0.5rem;
    border: 1px solid var(--tblr-border-color);
}

.empty-state .icon {
    width: 3rem;
    height: 3rem;
    color: var(--tblr-text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--tblr-text);
}

.empty-state p {
    color: var(--tblr-text-muted);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-header {
    border-bottom: 1px solid var(--tblr-border-color);
    padding: 1rem 1.5rem;
}

.modal-title {
    font-size: 1.125rem;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--tblr-border-color);
    padding: 1rem 1.5rem;
    gap: 0.5rem;
}

/* 表单样式 */
.form-label.required::after {
    content: " *";
    color: var(--tblr-danger);
}

.form-control:focus {
    border-color: var(--tblr-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--tblr-primary-rgb), 0.25);
}

/* 批量操作样式 */
.batch-actions {
    background-color: var(--tblr-bg-surface-secondary);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--tblr-border-color);
    display: none;
}

.batch-actions.show {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.batch-info {
    font-size: 0.875rem;
    color: var(--tblr-text-muted);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .page-header .col-auto {
        margin-left: 0 !important;
    }
    
    .search-actions-row .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-input-group {
        max-width: 100%;
    }
    
    .action-buttons {
        justify-content: flex-start;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
        border: 1px solid var(--tblr-border-color);
    }
    
    .manager-table th,
    .manager-table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    .action-btn {
        padding: 0.125rem 0.375rem;
        font-size: 0.6875rem;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
    
    .empty-state .icon {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-body,
    .modal-header,
    .modal-footer {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .btn-list {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-list .btn {
        width: 100%;
        justify-content: center;
    }
    
    .card-actions {
        margin-top: 0.5rem;
    }
    
    /* 在小屏幕上隐藏描述和创建时间列 */
    .manager-table th:nth-child(3),
    .manager-table td:nth-child(3),
    .manager-table th:nth-child(5),
    .manager-table td:nth-child(5) {
        display: none;
    }

    /* 调整剩余列的宽度 */
    .manager-table th:nth-child(2),
    .manager-table td:nth-child(2) {
        width: 40%;
    }

    .manager-table th:nth-child(4),
    .manager-table td:nth-child(4) {
        width: 20%;
        text-align: center;
    }

    .manager-table th:nth-child(6),
    .manager-table td:nth-child(6) {
        width: 25%;
        text-align: center;
    }
    
    .batch-actions {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-row {
    height: 3rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .skeleton {
        background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
        background-size: 200% 100%;
    }
}

/* 目录卡片样式 */
.directory-card {
    border: 1px solid var(--tblr-border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    background-color: var(--tblr-bg-surface);
    transition: all 0.2s ease-in-out;
    height: 100%;
    position: relative;
}

.directory-card:hover {
    border-color: var(--tblr-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.directory-card.selected {
    border-color: var(--tblr-primary);
    background-color: rgba(var(--tblr-primary-rgb), 0.05);
}

.directory-card .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0;
    border: none;
    background: none;
}

.directory-card .directory-icon {
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--tblr-primary);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.directory-card .directory-info h5 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--tblr-text);
}

.directory-card .directory-path {
    font-family: var(--tblr-font-monospace);
    font-size: 0.875rem;
    color: var(--tblr-text-muted);
    margin-top: 0.25rem;
}

.directory-card .directory-description {
    color: var(--tblr-text-muted);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.directory-card .directory-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.directory-card .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.directory-card .stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tblr-text);
}

.directory-card .stat-label {
    font-size: 0.75rem;
    color: var(--tblr-text-muted);
    margin-top: 0.25rem;
}

.directory-card .directory-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.directory-card .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.directory-card .status-badge.active {
    background-color: rgba(var(--tblr-success-rgb), 0.1);
    color: var(--tblr-success);
}

.directory-card .status-badge.inactive {
    background-color: rgba(var(--tblr-danger-rgb), 0.1);
    color: var(--tblr-danger);
}

.directory-card .directory-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: auto;
}

.directory-card .directory-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

.directory-card .directory-checkbox {
    position: absolute;
    top: 1rem;
    right: 1rem;
    cursor: pointer;
}

.directory-card .last-scan {
    font-size: 0.75rem;
    color: var(--tblr-text-muted);
    margin-top: 0.5rem;
}

/* 目录卡片响应式 */
@media (max-width: 768px) {
    .directory-card {
        padding: 1rem;
    }

    .directory-card .directory-stats {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .directory-card .directory-actions {
        flex-direction: column;
    }

    .directory-card .directory-actions .btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
}
