/**
 * Toast 通知管理模块
 * 提供成功、错误、警告、信息等类型的通知
 */

class ToastManager {
    constructor() {
        this.container = document.getElementById('toast-container');
        this.toastCounter = 0;
        this.maxToasts = 5; // 最大同时显示的 Toast 数量
    }

    /**
     * 显示 Toast 通知
     * @param {string} message - 通知消息
     * @param {string} type - 通知类型 ('success', 'error', 'warning', 'info')
     * @param {number} duration - 显示时长（毫秒），0 表示不自动关闭
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    show(message, type = 'info', duration = 5000, options = {}) {
        // 限制 Toast 数量
        this.limitToasts();

        const toastId = `toast-${++this.toastCounter}`;
        const toast = this.createToast(toastId, message, type, options);

        // 新的Toast插入到顶部
        if (this.container.firstChild) {
            this.container.insertBefore(toast, this.container.firstChild);
        } else {
            this.container.appendChild(toast);
        }

        // 添加淡入动画
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        toast.style.transition = 'all 0.3s ease-in-out';

        // 强制重排以确保初始样式生效
        toast.offsetHeight;

        // 显示动画
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';

        // 添加进度条功能
        if (duration > 0) {
            this.addProgressBar(toast, duration);

            // 自动隐藏
            setTimeout(() => {
                this.hide(toast);
            }, duration);
        }

        return toast;
    }

    /**
     * 限制 Toast 数量
     */
    limitToasts() {
        const toasts = this.container.querySelectorAll('.alert');
        if (toasts.length >= this.maxToasts) {
            // 移除最旧的 Toast
            const oldestToast = toasts[0];
            this.hide(oldestToast);
        }
    }

    /**
     * 创建 Toast 元素
     * @param {string} id - Toast ID
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    createToast(id, message, type, options = {}) {
        const toast = document.createElement('div');
        toast.id = id;
        toast.className = `alert alert-${this.getAlertType(type)} shadow-lg mb-2 relative overflow-hidden`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const icon = this.getTypeIcon(type);
        const title = options.title || this.getTypeTitle(type);
        const showTime = options.showTime !== false;

        toast.innerHTML = `
            <div class="flex">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    ${icon}
                </svg>
                <div class="flex-1">
                    <div class="font-bold">${title}</div>
                    <div class="text-sm">${message}</div>
                    ${showTime ? `<div class="text-xs opacity-70 mt-1">${this.getTimeString()}</div>` : ''}
                </div>
                <button type="button" class="btn btn-sm btn-circle btn-ghost ml-2" onclick="this.parentElement.parentElement.remove()" aria-label="关闭">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="toast-progress absolute bottom-0 left-0 h-1 bg-current opacity-30 z-10" style="width: 100%;"></div>
        `;

        return toast;
    }

    /**
     * 获取DaisyUI alert类型
     * @param {string} type - 通知类型
     * @returns {string} DaisyUI alert类型
     */
    getAlertType(type) {
        const alertTypes = {
            success: 'success',
            error: 'error',
            warning: 'warning',
            info: 'info'
        };
        return alertTypes[type] || 'info';
    }

    /**
     * 获取类型对应的图标
     * @param {string} type - 通知类型
     * @returns {string} SVG 图标路径
     */
    getTypeIcon(type) {
        const icons = {
            success: '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10"/>',
            error: '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 9v2m0 4v.01"/><path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"/>',
            warning: '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 9v2m0 4v.01"/><path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"/>',
            info: '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><line x1="12" y1="8" x2="12.01" y2="8"/><polyline points="11,12 12,12 12,16 13,16"/>'
        };
        return icons[type] || icons.info;
    }

    /**
     * 获取类型对应的标题
     * @param {string} type - 通知类型
     * @returns {string} 标题文本
     */
    getTypeTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '信息'
        };
        return titles[type] || titles.info;
    }

    /**
     * 获取当前时间字符串
     * @returns {string} 时间字符串
     */
    getTimeString() {
        const now = new Date();
        return now.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    /**
     * 显示成功通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    success(message, duration = 3000, options = {}) {
        return this.show(message, 'success', duration, options);
    }

    /**
     * 显示错误通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    error(message, duration = 8000, options = {}) {
        return this.show(message, 'error', duration, options);
    }

    /**
     * 显示警告通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    warning(message, duration = 5000, options = {}) {
        return this.show(message, 'warning', duration, options);
    }

    /**
     * 显示信息通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    info(message, duration = 4000, options = {}) {
        return this.show(message, 'info', duration, options);
    }

    /**
     * 清除所有 Toast 通知
     */
    clearAll() {
        const toasts = this.container.querySelectorAll('.alert');
        toasts.forEach(toast => {
            this.hide(toast);
        });
    }

    /**
     * 显示加载中通知
     * @param {string} message - 消息内容
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    loading(message = '加载中...', options = {}) {
        const toastId = `toast-loading-${++this.toastCounter}`;
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = 'alert alert-info shadow-lg mb-2';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const title = options.title || '加载中';

        toast.innerHTML = `
            <div class="flex">
                <span class="loading loading-spinner loading-sm shrink-0"></span>
                <div class="flex-1 ml-3">
                    <div class="font-bold">${title}</div>
                    <div class="text-sm">${message}</div>
                </div>
                <button type="button" class="btn btn-sm btn-circle btn-ghost ml-2" onclick="this.parentElement.parentElement.remove()" aria-label="关闭">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        `;

        // 新的Toast插入到顶部
        if (this.container.firstChild) {
            this.container.insertBefore(toast, this.container.firstChild);
        } else {
            this.container.appendChild(toast);
        }

        // 添加淡入动画
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        toast.style.transition = 'all 0.3s ease-in-out';

        // 强制重排以确保初始样式生效
        toast.offsetHeight;

        // 显示动画
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';

        return toast;
    }

    /**
     * 隐藏指定的 Toast
     * @param {HTMLElement} toast - Toast 元素
     */
    hide(toast) {
        if (!toast) return;

        // 添加淡出动画
        toast.style.transition = 'all 0.3s ease-in-out';
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';

        // 动画完成后移除元素
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }

    /**
     * 显示确认对话框样式的 Toast
     * @param {string} message - 消息内容
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     * @param {Object} options - 额外选项
     * @returns {HTMLElement} Toast 元素
     */
    confirm(message, onConfirm, onCancel, options = {}) {
        const toastId = `toast-confirm-${++this.toastCounter}`;
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = 'alert alert-warning shadow-lg mb-2';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const title = options.title || '确认操作';
        const confirmText = options.confirmText || '确认';
        const cancelText = options.cancelText || '取消';

        toast.innerHTML = `
            <div class="flex flex-col">
                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M12 9v2m0 4v.01"/>
                        <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"/>
                    </svg>
                    <div class="flex-1 ml-3">
                        <div class="font-bold">${title}</div>
                        <div class="text-sm mt-1">${message}</div>
                    </div>
                    <button type="button" class="btn btn-sm btn-circle btn-ghost ml-2" onclick="this.parentElement.parentElement.parentElement.remove()" aria-label="关闭">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="flex gap-2 mt-4 justify-end">
                    <button type="button" class="btn btn-sm btn-outline" data-action="cancel">
                        ${cancelText}
                    </button>
                    <button type="button" class="btn btn-sm btn-error" data-action="confirm">
                        ${confirmText}
                    </button>
                </div>
            </div>
        `;

        // 绑定按钮事件
        toast.addEventListener('click', (e) => {
            const action = e.target.getAttribute('data-action');
            if (action === 'confirm') {
                onConfirm && onConfirm();
                this.hide(toast);
            } else if (action === 'cancel') {
                onCancel && onCancel();
                this.hide(toast);
            }
        });

        // 新的Toast插入到顶部
        if (this.container.firstChild) {
            this.container.insertBefore(toast, this.container.firstChild);
        } else {
            this.container.appendChild(toast);
        }

        // 添加淡入动画
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        toast.style.transition = 'all 0.3s ease-in-out';

        // 强制重排以确保初始样式生效
        toast.offsetHeight;

        // 显示动画
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';

        return toast;
    }

    /**
     * 添加进度条功能
     * @param {HTMLElement} toast - Toast 元素
     * @param {number} duration - 持续时间（毫秒）
     */
    addProgressBar(toast, duration) {
        const progressBar = toast.querySelector('.toast-progress');
        if (!progressBar) return;

        // 设置初始状态 - 进度条从满宽度开始
        progressBar.style.width = '100%';
        progressBar.style.transition = `width ${duration}ms linear`;
        progressBar.style.transformOrigin = 'left center';

        // 开始进度条动画 - 宽度逐渐减少到0
        setTimeout(() => {
            progressBar.style.width = '0%';
        }, 50); // 小延迟确保动画正常开始

        // 鼠标悬停时暂停进度条
        let isPaused = false;
        let remainingTime = duration;
        let startTime = Date.now();

        const pauseProgress = () => {
            if (!isPaused) {
                isPaused = true;
                const elapsed = Date.now() - startTime;
                remainingTime = Math.max(0, duration - elapsed);
                const currentWidth = (remainingTime / duration) * 100;
                progressBar.style.transition = 'none';
                progressBar.style.width = `${currentWidth}%`;
            }
        };

        const resumeProgress = () => {
            if (isPaused && remainingTime > 0) {
                isPaused = false;
                startTime = Date.now();
                progressBar.style.transition = `width ${remainingTime}ms linear`;
                progressBar.style.width = '0%';
            }
        };

        toast.addEventListener('mouseenter', pauseProgress);
        toast.addEventListener('mouseleave', resumeProgress);

        // 触摸设备支持
        toast.addEventListener('touchstart', pauseProgress);
        toast.addEventListener('touchend', resumeProgress);
    }
}

// 创建全局 Toast 管理器实例
const toast = new ToastManager();

// 导出 Toast 管理器
window.toast = toast;
