"""add_favorites_table

Revision ID: c371a52160b1
Revises: b24d0915a4da
Create Date: 2025-07-30 17:43:44.855811

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c371a52160b1'
down_revision: Union[str, Sequence[str], None] = 'b24d0915a4da'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop old user-related tables if they exist
    try:
        op.drop_table('user_favorites')
    except:
        pass
    try:
        op.drop_index(op.f('ix_user_favorites_detail_id'), table_name='user_favorites_detail')
        op.drop_index(op.f('ix_user_favorites_detail_movie_id'), table_name='user_favorites_detail')
        op.drop_index(op.f('ix_user_favorites_detail_user_id'), table_name='user_favorites_detail')
        op.drop_table('user_favorites_detail')
    except:
        pass
    try:
        op.drop_index(op.f('ix_users_email'), table_name='users')
        op.drop_index(op.f('ix_users_id'), table_name='users')
        op.drop_index(op.f('ix_users_username'), table_name='users')
        op.drop_table('users')
    except:
        pass

    # Create new favorites table
    op.create_table('favorites',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('movie_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['movie_id'], ['movies.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_favorites_id'), 'favorites', ['id'], unique=False)
    op.create_index(op.f('ix_favorites_movie_id'), 'favorites', ['movie_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop favorites table
    op.drop_index(op.f('ix_favorites_movie_id'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_id'), table_name='favorites')
    op.drop_table('favorites')
    # ### end Alembic commands ###
