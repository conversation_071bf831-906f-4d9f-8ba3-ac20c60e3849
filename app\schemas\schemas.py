"""
Pydantic 模型定义
用于 API 请求验证和响应序列化
"""
from datetime import datetime
from typing import Optional, List, Any, Union
from pydantic import BaseModel, Field, validator


# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool
    message: str
    data: Optional[Any] = None


class PaginationParams(BaseModel):
    """分页参数模型"""
    limit: int = Field(default=50, ge=1, le=100, description="每页数量，1-100之间")
    offset: int = Field(default=0, ge=0, description="偏移量，从0开始")
    search: Optional[str] = Field(default=None, description="搜索关键词")


# 标签相关模型
class TagCreate(BaseModel):
    """创建标签请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="标签名称")
    description: Optional[str] = Field(default=None, max_length=500, description="标签描述")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('标签名称不能为空')
        return v.strip()


class TagUpdate(BaseModel):
    """更新标签请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100, description="标签名称")
    description: Optional[str] = Field(default=None, max_length=500, description="标签描述")
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('标签名称不能为空')
        return v.strip() if v else v


class TagResponse(BaseModel):
    """标签响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class TagListResponse(BaseResponse):
    """标签列表响应模型"""
    data: Optional[List[TagResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 分类相关模型
class GenreCreate(BaseModel):
    """创建分类请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=500, description="分类描述")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('分类名称不能为空')
        return v.strip()


class GenreUpdate(BaseModel):
    """更新分类请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=500, description="分类描述")
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('分类名称不能为空')
        return v.strip() if v else v


class GenreResponse(BaseModel):
    """分类响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class GenreListResponse(BaseResponse):
    """分类列表响应模型"""
    data: Optional[List[GenreResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 系列相关模型
class SeriesCreate(BaseModel):
    """创建系列请求模型"""
    name: str = Field(..., min_length=1, max_length=255, description="系列名称")
    description: Optional[str] = Field(default=None, max_length=1000, description="系列描述")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('系列名称不能为空')
        return v.strip()


class SeriesUpdate(BaseModel):
    """更新系列请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=255, description="系列名称")
    description: Optional[str] = Field(default=None, max_length=1000, description="系列描述")
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('系列名称不能为空')
        return v.strip() if v else v


class SeriesResponse(BaseModel):
    """系列响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class SeriesListResponse(BaseResponse):
    """系列列表响应模型"""
    data: Optional[List[SeriesResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 删除操作参数模型
class DeleteParams(BaseModel):
    """删除操作参数模型"""
    force: bool = Field(default=False, description="是否强制删除（忽略关联关系）")


# 统一删除相关模型
class UnifiedDeleteRequest(BaseModel):
    """统一删除请求模型（支持单项和批量删除）"""
    ids: Union[int, List[int]] = Field(..., description="要删除的ID（单个数字）或ID列表（数组）")
    force: bool = Field(default=False, description="是否强制删除（忽略关联关系）")

    @validator('ids')
    def validate_ids(cls, v):
        if isinstance(v, int):
            if v <= 0:
                raise ValueError('ID必须为正整数')
            return [v]  # 转换为列表格式统一处理
        elif isinstance(v, list):
            if not v:
                raise ValueError('ID列表不能为空')
            # 验证每个ID都是正整数
            for id_val in v:
                if not isinstance(id_val, int) or id_val <= 0:
                    raise ValueError('所有ID必须为正整数')
            # 去重并保持顺序
            seen = set()
            unique_ids = []
            for id_val in v:
                if id_val not in seen:
                    seen.add(id_val)
                    unique_ids.append(id_val)
            return unique_ids
        else:
            raise ValueError('ids必须是整数或整数列表')


# 保持向后兼容的批量删除模型
class BatchDeleteRequest(BaseModel):
    """批量删除请求模型（向后兼容）"""
    ids: List[int] = Field(..., min_items=1, description="要删除的ID列表")
    force: bool = Field(default=False, description="是否强制删除（忽略关联关系）")

    @validator('ids')
    def validate_ids(cls, v):
        if not v:
            raise ValueError('ID列表不能为空')
        # 去重并保持顺序
        seen = set()
        unique_ids = []
        for id_val in v:
            if id_val not in seen:
                seen.add(id_val)
                unique_ids.append(id_val)
        return unique_ids


class BatchDeleteFailedItem(BaseModel):
    """批量删除失败项模型"""
    id: int = Field(..., description="失败的ID")
    error: str = Field(..., description="失败原因")


class BatchDeleteResult(BaseModel):
    """批量删除结果模型"""
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    success_ids: List[int] = Field(default_factory=list, description="成功删除的ID列表")
    failed_items: List[BatchDeleteFailedItem] = Field(default_factory=list, description="失败项列表")


class BatchDeleteResponse(BaseResponse):
    """批量删除响应模型"""
    data: Optional[BatchDeleteResult] = None


# 统计信息模型
class EntityStats(BaseModel):
    """实体统计信息模型"""
    total_count: int
    used_count: int  # 被电影使用的数量
    unused_count: int  # 未被使用的数量


# 演员相关模型
class ActorCreate(BaseModel):
    """创建演员请求模型"""
    name: str = Field(..., min_length=1, max_length=255, description="演员姓名")
    role: Optional[str] = Field(default=None, max_length=255, description="角色名称")
    biography: Optional[str] = Field(default=None, max_length=1000, description="演员简介")
    actor_type: Optional[str] = Field(default="Actor", max_length=50, description="演员类型")

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('演员姓名不能为空')
        return v.strip()


class ActorUpdate(BaseModel):
    """更新演员请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=255, description="演员姓名")
    role: Optional[str] = Field(default=None, max_length=255, description="角色名称")
    biography: Optional[str] = Field(default=None, max_length=1000, description="演员简介")
    actor_type: Optional[str] = Field(default=None, max_length=50, description="演员类型")

    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('演员姓名不能为空')
        return v.strip() if v else v


class ActorResponse(BaseModel):
    """演员响应模型"""
    id: int
    name: str
    role: Optional[str]
    actor_type: Optional[str]
    biography: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class ActorListResponse(BaseResponse):
    """演员列表响应模型"""
    data: Optional[List[ActorResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 影片相关模型


class MovieUpdate(BaseModel):
    """更新影片请求模型"""
    title: Optional[str] = Field(default=None, min_length=1, max_length=255, description="影片标题")
    original_title: Optional[str] = Field(default=None, max_length=255, description="原始标题")
    year: Optional[int] = Field(default=None, ge=1900, le=2100, description="年份")
    rating: Optional[float] = Field(default=None, ge=0, le=10, description="评分")
    runtime: Optional[int] = Field(default=None, ge=0, description="时长（分钟）")
    plot: Optional[str] = Field(default=None, description="剧情简介")
    outline: Optional[str] = Field(default=None, description="简短描述")
    country: Optional[str] = Field(default=None, max_length=100, description="国家")
    critic_rating: Optional[float] = Field(default=None, ge=0, le=10, description="影评人评分")
    sort_title: Optional[str] = Field(default=None, max_length=255, description="排序标题")
    trailer: Optional[str] = Field(default=None, max_length=500, description="预告片链接")
    num: Optional[str] = Field(default=None, max_length=50, description="编号")
    series_id: Optional[int] = Field(default=None, description="系列ID")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表")
    genre_ids: Optional[List[int]] = Field(default=None, description="分类ID列表")
    actor_ids: Optional[List[int]] = Field(default=None, description="演员ID列表")

    @validator('title')
    def validate_title(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('影片标题不能为空')
        return v.strip() if v else v


class MovieResponse(BaseModel):
    """影片响应模型"""
    id: int
    title: str
    original_title: Optional[str]
    year: Optional[int]
    rating: Optional[float]
    runtime: Optional[int]
    plot: Optional[str]
    outline: Optional[str]
    country: Optional[str]
    critic_rating: Optional[float]
    sort_title: Optional[str]
    trailer: Optional[str]
    num: Optional[str]
    file_path: str
    poster_uuid: Optional[str] = None  # 虚拟海报 UUID
    fanart_uuid: Optional[str] = None  # 虚拟剧照 UUID
    thumb_uuid: Optional[str] = None   # 虚拟缩略图 UUID
    created_at: datetime
    updated_at: datetime

    # 关联数据
    series: Optional[SeriesResponse] = None
    tags: Optional[List[TagResponse]] = None
    genres: Optional[List[GenreResponse]] = None
    actors: Optional[List[ActorResponse]] = None

    model_config = {"from_attributes": True}


class MovieListItem(BaseModel):
    """影片列表项模型（简化版）"""
    id: int
    title: str
    year: Optional[int]
    rating: Optional[float]
    runtime: Optional[int]
    poster_uuid: Optional[str] = None    # 虚拟海报 UUID
    fanart_uuid: Optional[str] = None    # 虚拟剧照 UUID
    thumb_uuid: Optional[str] = None     # 虚拟缩略图 UUID
    series_name: Optional[str] = None
    genre_count: Optional[int] = 0
    tag_count: Optional[int] = 0
    actor_count: Optional[int] = 0

    model_config = {"from_attributes": True}


class MovieListResponse(BaseResponse):
    """影片列表响应模型"""
    data: Optional[List[MovieListItem]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


class MovieFilterParams(BaseModel):
    """影片过滤参数模型"""
    limit: int = Field(default=50, ge=1, le=100, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词（标题、导演、演员）")
    genre_ids: Optional[List[int]] = Field(default=None, description="分类ID列表过滤")
    series_ids: Optional[List[int]] = Field(default=None, description="系列ID列表过滤")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表过滤")
    year: Optional[int] = Field(default=None, ge=1900, le=2100, description="年份过滤")
    year_from: Optional[int] = Field(default=None, ge=1900, le=2100, description="起始年份过滤")
    year_to: Optional[int] = Field(default=None, ge=1900, le=2100, description="结束年份过滤")
    rating_min: Optional[float] = Field(default=None, ge=0, le=10, description="最低评分过滤")
    rating_from: Optional[float] = Field(default=None, ge=0, le=10, description="起始评分过滤")
    rating_to: Optional[float] = Field(default=None, ge=0, le=10, description="结束评分过滤")
    directory_id: Optional[int] = Field(default=None, description="目录ID过滤")
    sort_by: Optional[str] = Field(default="title", description="排序字段")
    sort_order: Optional[str] = Field(default="asc", description="排序方向")


class BatchAssociationRequest(BaseModel):
    """批量关联操作请求模型"""
    movie_ids: List[int] = Field(..., min_items=1, description="影片ID列表")
    tag_ids: Optional[List[int]] = Field(None, description="要操作的标签ID列表")
    genre_ids: Optional[List[int]] = Field(None, description="要操作的分类ID列表")
    series_id: Optional[int] = Field(None, description="要操作的系列ID")


class FavoriteFilterParams(BaseModel):
    """收藏过滤参数模型"""
    limit: int = Field(default=50, ge=1, le=100, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词（标题、导演、演员）")
    genre_ids: Optional[List[int]] = Field(default=None, description="分类ID列表过滤")
    series_ids: Optional[List[int]] = Field(default=None, description="系列ID列表过滤")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表过滤")
    year_from: Optional[int] = Field(default=None, ge=1900, le=2100, description="起始年份过滤")
    year_to: Optional[int] = Field(default=None, ge=1900, le=2100, description="结束年份过滤")
    rating_from: Optional[float] = Field(default=None, ge=0, le=10, description="起始评分过滤")
    rating_to: Optional[float] = Field(default=None, ge=0, le=10, description="结束评分过滤")
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", description="排序方向")


# ========== 收藏相关 Schema ==========

class FavoriteResponse(BaseModel):
    """收藏响应模型"""
    id: int
    movie_id: int
    created_at: datetime
    updated_at: datetime

    # 关联的影片信息
    movie: Optional[MovieListItem] = None

    model_config = {"from_attributes": True}


class FavoriteStatusResponse(BaseModel):
    """收藏状态响应模型"""
    movie_id: int
    is_favorited: bool
    favorite_id: Optional[int] = None
    favorited_at: Optional[datetime] = None


class BatchFavoriteRequest(BaseModel):
    """批量收藏请求模型"""
    movie_ids: List[int] = Field(..., min_length=1, max_length=100, description="影片ID列表")


class BatchFavoriteResponse(BaseModel):
    """批量收藏响应模型"""
    success_count: int
    failed_count: int
    success_movie_ids: List[int]
    failed_movie_ids: List[int]
    errors: List[str] = []


class FavoriteListResponse(BaseResponse):
    """收藏列表响应模型"""
    data: Optional[List[FavoriteResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 映射规则相关模型
class MappingRuleCreate(BaseModel):
    """创建映射规则请求模型"""
    original_value: str = Field(..., min_length=1, max_length=255, description="原始值")
    mapped_value: Optional[str] = Field(default=None, max_length=255, description="映射值，为空表示删除")

    @validator('original_value')
    def validate_original_value(cls, v):
        if not v or not v.strip():
            raise ValueError('原始值不能为空')
        return v.strip()

    @validator('mapped_value')
    def validate_mapped_value(cls, v):
        return v.strip() if v else v


class MappingRuleUpdate(BaseModel):
    """更新映射规则请求模型"""
    original_value: Optional[str] = Field(default=None, min_length=1, max_length=255, description="原始值")
    mapped_value: Optional[str] = Field(default=None, max_length=255, description="映射值，为空表示删除")

    @validator('original_value')
    def validate_original_value(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('原始值不能为空')
        return v.strip() if v else v

    @validator('mapped_value')
    def validate_mapped_value(cls, v):
        return v.strip() if v else v


class MappingRuleResponse(BaseModel):
    """映射规则响应模型"""
    id: int
    type: str
    original_value: str
    mapped_value: Optional[str]
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MappingRuleBatchCreate(BaseModel):
    """批量创建映射规则请求模型"""
    rules: List[MappingRuleCreate] = Field(..., min_items=1, description="映射规则列表")
    overwrite: bool = Field(default=False, description="是否覆盖已存在的规则")


class MappingRuleBatchResponse(BaseModel):
    """批量操作映射规则响应模型"""
    success_count: int
    failed_count: int
    success_rules: List[MappingRuleResponse]
    failed_rules: List[dict]
    errors: List[str] = []


class MappingRuleListResponse(BaseResponse):
    """映射规则列表响应模型"""
    data: Optional[List[MappingRuleResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


class MappingRuleExportResponse(BaseModel):
    """映射规则导出响应模型"""
    type: str
    rules: List[dict]
    exported_at: datetime
    total_count: int
