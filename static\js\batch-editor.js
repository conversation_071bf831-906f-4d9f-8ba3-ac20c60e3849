/**
 * 批量编辑器
 * 提供一个模态框，用于批量为影片添加或移除标签、分类和系列
 */
class BatchEditor {
    constructor() {
        this.modal = null;
        this.movieIds = [];
    }

    /**
     * 打开批量编辑器
     * @param {number[]} movieIds - 选中的影片ID列表
     */
    open(movieIds) {
        if (!movieIds || movieIds.length === 0) {
            toast.warning("请先选择要编辑的影片");
            return;
        }
        this.movieIds = movieIds;
        this.createModal();
        this.loadData();
        this.modal.showModal();
    }

    /**
     * 创建模态框HTML
     */
    createModal() {
        if (document.getElementById('batch-editor-modal')) return;

        const modalHtml = `
            <dialog id="batch-editor-modal" class="modal">
                <div class="modal-box w-11/12 max-w-2xl">
                    <h3 class="font-bold text-lg">批量编辑 ${this.movieIds.length} 部影片</h3>
                    
                    <!-- 表单内容 -->
                    <div class="form-control space-y-4 mt-4">
                        <!-- 标签编辑 -->
                        <div>
                            <label class="label">
                                <span class="label-text">编辑标签</span>
                            </label>
                            <div class="grid grid-cols-2 gap-4">
                                <div id="add-tags-dropdown"></div>
                                <div id="remove-tags-dropdown"></div>
                            </div>
                        </div>

                        <!-- 分类编辑 -->
                        <div>
                            <label class="label">
                                <span class="label-text">编辑分类</span>
                            </label>
                            <div class="grid grid-cols-2 gap-4">
                                <div id="add-genres-dropdown"></div>
                                <div id="remove-genres-dropdown"></div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-action mt-6">
                        <button id="save-batch-edit-btn" class="btn btn-primary">保存</button>
                        <button id="close-batch-edit-btn" class="btn">取消</button>
                    </div>
                </div>
            </dialog>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('batch-editor-modal');
        this.bindModalEvents();
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        document.getElementById('close-batch-edit-btn').addEventListener('click', () => {
            this.modal.close();
        });

        document.getElementById('save-batch-edit-btn').addEventListener('click', () => {
            this.saveChanges();
        });
    }

    /**
     * 加载模态框所需数据
     */
    async loadData() {
        try {
            // 并行获取所有选项
            const [tagsRes, genresRes] = await Promise.all([
                api.getTags(),
                api.getGenres()
            ]);

            const allTags = tagsRes.data || [];
            const allGenres = genresRes.data || [];

            // 获取选中影片的详细信息
            const moviePromises = this.movieIds.map(id => api.getMovie(id));
            const movieResponses = await Promise.all(moviePromises);
            const selectedMovies = movieResponses.map(res => res.data);

            // 计算共同的标签、分类
            const commonTags = this.getCommonItems(selectedMovies, 'tags');
            const commonGenres = this.getCommonItems(selectedMovies, 'genres');

            // 初始化下拉框
            this.addTagsDropdown = new MultiSelectDropdown('add-tags-dropdown', { placeholder: '添加标签' });
            this.removeTagsDropdown = new MultiSelectDropdown('remove-tags-dropdown', { placeholder: '移除标签' });
            this.addGenresDropdown = new MultiSelectDropdown('add-genres-dropdown', { placeholder: '添加分类' });
            this.removeGenresDropdown = new MultiSelectDropdown('remove-genres-dropdown', { placeholder: '移除分类' });

            // 填充数据
            this.addTagsDropdown.setData(allTags);
            this.removeTagsDropdown.setData(commonTags);
            this.addGenresDropdown.setData(allGenres);
            this.removeGenresDropdown.setData(commonGenres);

        } catch (error) {
            handleApiError(error, '加载批量编辑数据');
            this.modal.close();
        }
    }

    /**
     * 获取多个影片共同拥有的项目（如标签、分类）
     * @param {Object[]} movies - 影片对象列表
     * @param {string} key - 要比较的键名 (e.g., 'tags')
     * @returns {Object[]} 共同的项目列表
     */
    getCommonItems(movies, key) {
        if (!movies || movies.length === 0) return [];

        let commonItemIds = new Set(movies[0][key].map(item => item.id));

        for (let i = 1; i < movies.length; i++) {
            const currentItemIds = new Set(movies[i][key].map(item => item.id));
            commonItemIds.forEach(id => {
                if (!currentItemIds.has(id)) {
                    commonItemIds.delete(id);
                }
            });
        }

        return movies[0][key].filter(item => commonItemIds.has(item.id));
    }

    /**
     * 填充下拉列表
     * @param {string} selectId - 下拉列表的ID
     * @param {Object[]} items - 选项数据
     * @param {string} valueKey - 作为值的键名
     * @param {string} textKey - 作为文本的键名
     * @param {boolean} includeEmpty - 是否包含空选项
     */
    populateSelect(selectId, items, valueKey, textKey, includeEmpty = false) {
        const select = document.getElementById(selectId);
        if (!select) return;

        select.innerHTML = '';
        if (includeEmpty) {
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '无';
            select.appendChild(emptyOption);
        }

        items.forEach(item => {
            const option = document.createElement('option');
            option.value = item[valueKey];
            option.textContent = item[textKey];
            select.appendChild(option);
        });
    }

    /**
     * 保存更改
     */
    async saveChanges() {
        const addRequest = { movie_ids: this.movieIds };
        const removeRequest = { movie_ids: this.movieIds };

        // 获取要添加的ID
        addRequest.tag_ids = this.addTagsDropdown.getSelectedValues();
        addRequest.genre_ids = this.addGenresDropdown.getSelectedValues();

        // 获取要移除的ID
        removeRequest.tag_ids = this.removeTagsDropdown.getSelectedValues();
        removeRequest.genre_ids = this.removeGenresDropdown.getSelectedValues();

        try {
            const promises = [];
            if (addRequest.tag_ids.length > 0 || addRequest.genre_ids.length > 0) {
                promises.push(api.post('/movies/batch/add-associations', addRequest));
            }
            if (removeRequest.tag_ids.length > 0 || removeRequest.genre_ids.length > 0) {
                promises.push(api.post('/movies/batch/remove-associations', removeRequest));
            }

            if (promises.length > 0) {
                await Promise.all(promises);
                toast.success("批量编辑成功！");
            }

            this.modal.close();
            // 刷新页面以查看更改
            window.location.reload();

        } catch (error) {
            handleApiError(error, '保存批量编辑');
        }
    }

    /**
     * 获取 select 元素中选中的值
     * @param {string} selectId - select 元素的 ID
     * @returns {string[]} 选中的值的数组
     */
    getSelectedValues(selectId) {
        const select = document.getElementById(selectId);
        if (!select) return [];
        return Array.from(select.selectedOptions).map(option => option.value);
    }
}

window.batchEditor = new BatchEditor();
