# 页面标题区域样式统一优化

## 概述
本次更新统一了所有页面的标题区域样式，使其与 dashboard.html 保持完全一致的设计风格。

## 修改的文件列表
1. `templates/movies.html` - 影片库页面
2. `templates/tags_manager.html` - 标签管理页面
3. `templates/genres_manager.html` - 分类管理页面
4. `templates/actors_manager.html` - 演员管理页面
5. `templates/series_manager.html` - 系列管理页面
6. `templates/directories_manager.html` - 目录管理页面
7. `templates/settings.html` - 设置页面
8. `templates/movie_detail.html` - 影片详情页面
9. `templates/movie_edit.html` - 影片编辑页面

## 统一的设计标准

### 1. 背景和边框样式
- 背景渐变：`bg-gradient-to-r from-primary/10 to-secondary/10`
- 底部边框：`border-b border-base-300`
- 容器样式：`container mx-auto px-4 py-8`

### 2. 标题样式
- 标题文字：`text-3xl font-bold text-base-content`
- 图标大小：`w-8 h-8 text-primary`
- 标题布局：`flex items-center gap-3`
- 描述文字：`text-base-content/70 mt-2`

### 3. 响应式布局
- 主容器：`flex flex-col md:flex-row md:items-center md:justify-between gap-4`
- 按钮容器：`flex items-center gap-3`
- 按钮尺寸：统一使用 `btn-sm` 小尺寸

### 4. 图标映射
每个页面使用与导航菜单中相对应的 SVG 图标：

- **仪表板**: 网格图标 (4个方块)
- **影片库**: 文档图标 (带线条的文档)
- **标签管理**: 标签图标 (价格标签形状)
- **分类管理**: 分类图标 (带分隔线的矩形)
- **演员管理**: 用户图标 (人物轮廓)
- **系列管理**: 系列图标 (奖杯形状)
- **目录管理**: 文件夹图标
- **设置**: 齿轮图标
- **影片详情**: 文档图标 (与影片库相同)
- **影片编辑**: 编辑图标 (铅笔)

## 特殊处理

### movie_detail.html 和 movie_edit.html
这两个页面包含返回按钮，放置在标题上方：
```html
<div class="mb-3">
    <a href="#" class="btn btn-outline btn-sm">
        <svg>...</svg>
        返回按钮
    </a>
</div>
```

### settings.html
从 Bootstrap 样式转换为 DaisyUI 样式，保持功能完整性。

## 功能保持
- 所有原有的操作按钮都被保留并移动到标题区域右侧
- 所有 JavaScript 功能保持正常工作
- 按钮的 ID 和事件绑定保持不变
- 响应式设计在所有屏幕尺寸下正常显示

## 样式一致性
- 与 DaisyUI v5 组件库设计风格完全一致
- 所有页面在视觉上完全统一
- 保持了现代化的渐变背景效果
- 统一的间距和排版

## 技术细节
- 使用 Jinja2 模板的 `{% block page_header %}` 块
- 保持与 `base.html` 模板的继承结构
- 所有 SVG 图标使用内联方式，确保加载性能
- 使用 Tailwind CSS 类名，与项目技术栈保持一致

## 验证结果
- 所有模板文件通过语法检查
- 没有发现任何诊断错误
- 保持了原有功能的完整性
- 实现了完全统一的视觉效果
