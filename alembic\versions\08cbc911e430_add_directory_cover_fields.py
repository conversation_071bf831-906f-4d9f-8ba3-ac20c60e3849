"""Add directory cover fields

Revision ID: 08cbc911e430
Revises: d79bf0ef86f1
Create Date: 2025-07-30 12:33:46.575036

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '08cbc911e430'
down_revision: Union[str, Sequence[str], None] = 'd79bf0ef86f1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('directories', sa.Column('cover_image_base64', sa.Text(), nullable=True))
    op.add_column('directories', sa.Column('cover_generated_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('directories', 'cover_generated_at')
    op.drop_column('directories', 'cover_image_base64')
    # ### end Alembic commands ###
