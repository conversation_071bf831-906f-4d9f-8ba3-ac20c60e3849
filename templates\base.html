<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}媒体管理器{% endblock %}</title>

    <!-- DaisyUI CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
    <!-- Tailwind CSS Browser -->
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <!-- DaisyUI Themes -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@5/themes.css" rel="stylesheet" type="text/css" />

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">


    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="min-h-screen bg-base-100">
        <!-- Header -->
        <div class="navbar bg-base-100 shadow-lg fixed top-0 left-0 right-0 z-50">
            <div class="navbar-start">
                <div class="dropdown">
                    <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                        <i class="bi bi-list text-xl" aria-label="菜单图标"></i>
                    </div>
                    <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
                        <li>
                            <a href="/" class="nav-link">
                                <i class="bi bi-grid-3x3-gap" aria-label="仪表板图标"></i>
                                仪表板
                            </a>
                        </li>
                        <li>
                            <a href="/movies" class="nav-link">
                                <i class="bi bi-film" aria-label="影片图标"></i>
                                影片库
                            </a>
                        </li>
                        <li>
                            <a href="/favorites" class="nav-link">
                                <i class="bi bi-heart-fill" aria-label="收藏图标"></i>
                                收藏夹
                            </a>
                        </li>
                        <li>
                            <a>
                                <i class="bi bi-gear" aria-label="管理图标"></i>
                                管理
                            </a>
                            <ul class="p-2">
                                <li>
                                    <a href="/directories" class="management-link">
                                        <i class="bi bi-folder" aria-label="目录图标"></i>
                                        目录管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/tags" class="management-link">
                                        <i class="bi bi-tags" aria-label="标签图标"></i>
                                        标签管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/genres" class="management-link">
                                        <i class="bi bi-collection" aria-label="分类图标"></i>
                                        分类管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/series" class="management-link">
                                        <i class="bi bi-collection-play" aria-label="系列图标"></i>
                                        系列管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/actors" class="management-link">
                                        <i class="bi bi-people" aria-label="演员图标"></i>
                                        演员管理
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="/settings" class="nav-link">
                                <i class="bi bi-gear-fill" aria-label="设置图标"></i>
                                设置
                            </a>
                        </li>
                    </ul>
                </div>
                <a href="/" class="btn btn-ghost text-xl">
                    <i class="bi bi-camera-reels text-primary" style="font-size: 1.5rem;" aria-label="媒体管理器图标"></i>
                    媒体管理器
                </a>
            </div>
            <div class="navbar-center hidden lg:flex">
                <ul class="menu menu-horizontal px-1">
                    <li>
                        <a href="/" class="nav-link">
                            <i class="bi bi-grid-3x3-gap" aria-label="仪表板图标"></i>
                            仪表板
                        </a>
                    </li>
                    <li>
                        <a href="/movies" class="nav-link">
                            <i class="bi bi-film" aria-label="影片图标"></i>
                            影片库
                        </a>
                    </li>
                    <li>
                        <a href="/favorites" class="nav-link">
                            <i class="bi bi-heart-fill" aria-label="收藏图标"></i>
                            收藏夹
                        </a>
                    </li>
                    <li>
                        <details>
                            <summary>
                                <i class="bi bi-gear" aria-label="管理图标"></i>
                                管理
                            </summary>
                            <ul class="p-2 w-30">
                                <li>
                                    <a href="/directories" class="management-link">
                                        <i class="bi bi-folder" aria-label="目录图标"></i>
                                        目录管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/tags" class="management-link">
                                        <i class="bi bi-tags" aria-label="标签图标"></i>
                                        标签管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/genres" class="management-link">
                                        <i class="bi bi-collection" aria-label="分类图标"></i>
                                        分类管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/series" class="management-link">
                                        <i class="bi bi-collection-play" aria-label="系列图标"></i>
                                        系列管理
                                    </a>
                                </li>
                                <li>
                                    <a href="/actors" class="management-link">
                                        <i class="bi bi-people" aria-label="演员图标"></i>
                                        演员管理
                                    </a>
                                </li>
                            </ul>
                        </details>
                    </li>
                    <li>
                        <a href="/settings" class="nav-link">
                            <i class="bi bi-gear-fill" aria-label="设置图标"></i>
                            设置
                        </a>
                    </li>
                </ul>
            </div>
            <div class="navbar-end">
                <!-- 主题切换下拉菜单 -->
                <div class="dropdown dropdown-end theme-dropdown">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-circle" aria-label="主题切换">
                        <i class="bi bi-palette text-lg" aria-label="主题切换图标"></i>
                    </div>
                    <div tabindex="0" class="dropdown-content z-[1] bg-base-100 rounded-box w-80 mt-3 shadow-xl border border-base-300">
                        <!-- 主题选择器标题 -->
                        <div class="p-4 border-b border-base-300">
                            <h3 class="font-semibold text-base-content flex items-center gap-2">
                                <i class="bi bi-palette" aria-label="主题图标"></i>
                                选择主题
                            </h3>
                            <p class="text-sm text-base-content/70 mt-1">当前主题：<span id="current-theme-name" class="font-medium">浅色</span></p>
                        </div>
                        <!-- 主题网格 -->
                        <div class="p-4 max-h-96 overflow-y-auto">
                            <div id="theme-grid" class="grid grid-cols-2 gap-2">
                                <!-- 主题选项将由 JavaScript 动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <main class="flex-1 pt-16">
            {% block page_header %}{% endblock %}

            <!-- Page Body -->
            <div class="container mx-auto px-4 py-6">
                {% block content %}{% endblock %}
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer footer-center p-4 bg-base-300 text-base-content">
            <div class="flex flex-col md:flex-row justify-between items-center w-full max-w-6xl">
                <div class="text-center md:text-left">
                    <a href="https://github.com/KleinerSource" class="link link-hover" target="_blank">
                        © 2025 KleinerSource. All rights reserved.
                    </a>
                </div>
                <div class="flex flex-col md:flex-row gap-4 text-center">
                    <span>版本 v0.9.4</span>
                    <a href="/health" class="link link-hover">系统状态</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Movie Detail Modal -->
    <dialog id="movie-detail-modal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl">
            <form method="dialog">
                <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
            </form>
            <h3 class="font-bold text-lg" id="movie-detail-title">影片详情</h3>
            <div class="py-4" id="movie-detail-content">
                <!-- Movie details will be loaded here -->
            </div>
            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('movie-detail-modal').close()">关闭</button>
                <button type="button" class="btn btn-warning" id="edit-movie-btn">编辑</button>
                <button type="button" class="btn btn-error" id="delete-movie-btn">删除</button>
            </div>
        </div>
    </dialog>

    <!-- Movie Edit Modal -->
    <dialog id="movie-edit-modal" class="modal">
        <div class="modal-box w-11/12 max-w-5xl">
            <form method="dialog">
                <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
            </form>
            <h3 class="font-bold text-lg">编辑影片</h3>
            <div class="py-4" id="movie-edit-content">
                <!-- Edit form will be loaded here -->
            </div>
            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('movie-edit-modal').close()">取消</button>
                <button type="button" class="btn btn-primary" id="save-movie-btn">保存</button>
            </div>
        </div>
    </dialog>

    <!-- Delete Confirmation Modal -->
    <dialog id="delete-confirm-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">确认删除</h3>
            <p class="py-4">您确定要删除这部影片吗？此操作不可撤销。</p>
            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('delete-confirm-modal').close()">取消</button>
                <button type="button" class="btn btn-error" id="confirm-delete-btn">删除</button>
            </div>
        </div>
    </dialog>



    <!-- Toast Container -->
    <div class="toast toast-top toast-end z-50" id="toast-container">
        <!-- Toasts will be added here -->
    </div>

    <!-- jQuery (for compatibility with existing code) -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    
    <!-- Core JS Modules -->
    <script src="{{ url_for('static', path='/js/utils.js') }}"></script>
    <script src="{{ url_for('static', path='/js/api.js') }}"></script>
    <script src="{{ url_for('static', path='/js/toast.js') }}"></script>
    <script src="{{ url_for('static', path='/js/theme-manager.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
    
    <!-- Initialize App -->
    <script>


        // 设置导航激活状态
        function setActiveNavigation() {
            const currentPath = window.location.pathname;

            // 移除所有活动状态
            document.querySelectorAll('.nav-link, .management-link').forEach(link => {
                link.classList.remove('btn-active');
            });
            document.querySelectorAll('.dropdown-hover .btn').forEach(btn => {
                btn.classList.remove('btn-active');
            });

            // 添加当前页面的活动状态
            const activeLink = document.querySelector(`.nav-link[href="${currentPath}"]`);
            if (activeLink) {
                activeLink.classList.add('btn-active');
            }

            // 处理管理菜单的子页面
            const managementPaths = ['/directories', '/tags', '/genres', '/series', '/actors'];
            if (managementPaths.includes(currentPath)) {
                // 激活管理下拉菜单按钮
                const managementDropdown = document.querySelector('.dropdown-hover .btn');
                if (managementDropdown) {
                    managementDropdown.classList.add('btn-active');
                }

                // 激活对应的管理菜单项
                const activeManagementLink = document.querySelector(`.management-link[href="${currentPath}"]`);
                if (activeManagementLink) {
                    activeManagementLink.classList.add('btn-active');
                }
            }

            // 处理收藏页面
            if (currentPath === '/favorites') {
                const favoriteLinks = document.querySelectorAll('a[href="/favorites"]');
                favoriteLinks.forEach(link => {
                    link.classList.add('btn-active');
                });
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前页面的导航状态
            setActiveNavigation();

            // 初始化下拉菜单自动关闭功能
            initDropdownAutoClose();
        });

        // 下拉菜单自动关闭功能
        function initDropdownAutoClose() {
            // 点击外部区域关闭下拉菜单
            document.addEventListener('click', function(event) {
                const dropdowns = document.querySelectorAll('.dropdown');
                dropdowns.forEach(dropdown => {
                    if (!dropdown.contains(event.target)) {
                        const trigger = dropdown.querySelector('[tabindex="0"]');
                        if (trigger) {
                            trigger.blur();
                        }
                    }
                });
            });

        }
    </script>
</body>
</html>
