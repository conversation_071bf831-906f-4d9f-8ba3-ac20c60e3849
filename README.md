# FastAPI 媒体管理器 (Media Manager)

一个类似 Jellyfin 的媒体管理器系统，使用 FastAPI 构建，支持媒体文件扫描、NFO 元数据解析和 RESTful API。

## 功能特性

### 已实现功能
- ✅ **媒体文件扫描**：自动扫描指定目录下的视频文件
- ✅ **NFO 文件解析**：解析 Kodi/Jellyfin 格式的 XML 元数据文件
- ✅ **图片文件识别**：自动识别海报、背景图、缩略图等
- ✅ **数据库架构**：完整的 SQLAlchemy 模型设计
- ✅ **RESTful API**：基于 FastAPI 的 API 接口
- ✅ **跨平台兼容**：支持 Windows、Linux、macOS

### 数据库架构
- **movies**：电影主表，存储基本信息和文件路径
- **tags**：标签表，支持完整 CRUD 操作
- **genres**：类型表，支持完整 CRUD 操作
- **actors**：演员表，支持完整 CRUD 操作
- **series**：系列/合集表
- **directories**：媒体目录配置表
- **v_images**：虚拟图片映射表（UUID 访问）
- **configs**：系统配置表

### NFO 解析支持
支持解析以下元数据字段：
- 基本信息：标题、年份、评分、时长、剧情等
- 分类信息：类型、标签、制片厂
- 人员信息：演员姓名、角色、类型
- 系列信息：电影系列/合集
- 日期信息：添加日期、首映日期、发布日期

## 技术栈

- **后端框架**：FastAPI
- **数据库 ORM**：SQLAlchemy
- **数据库迁移**：Alembic
- **包管理器**：uv
- **数据库**：SQLite（跨平台兼容）
- **Python 版本**：3.11+

## 快速开始

### 1. 环境准备
```bash
# 确保已安装 uv
uv --version

# 克隆项目
git clone <repository-url>
cd FastAPI_MDC
```

### 2. 安装依赖
```bash
# 同步项目依赖
uv sync
```

### 3. 数据库初始化
```bash
# 运行数据库迁移
uv run alembic upgrade head
```

### 4. 启动服务
```bash
# 启动 FastAPI 服务器
uv run python main.py
```

服务器将在 `http://localhost:8000` 启动。

### 5. 测试功能
```bash
# 测试媒体扫描和解析
uv run python test_complete.py

# 测试 API 接口
uv run python test_api.py
```

## API 接口

### 基础接口
- `GET /`：API 信息
- `GET /health`：健康检查

### 媒体管理接口
- `POST /scan`：扫描媒体目录
  - 参数：`directory`（目录路径）、`recursive`（是否递归）
  - 返回：扫描结果和解析的元数据

## 项目结构

```
FastAPI_MDC/
├── app/                    # 应用主目录
│   ├── models/            # 数据库模型
│   ├── services/          # 业务逻辑服务
│   ├── api/               # API 路由
│   └── core/              # 核心配置
├── alembic/               # 数据库迁移
├── videos/                # 示例媒体文件
├── data/                  # 数据库文件
├── main.py                # FastAPI 应用入口
├── pyproject.toml         # 项目配置
└── changelog.txt          # 更新日志
```

## 开发计划

### 下一步功能
- 🔄 **完整 CRUD API**：电影、标签、类型、演员管理
- 🔄 **图片虚拟化访问**：UUID 映射真实文件路径
- 🔄 **数据库同步回 NFO**：支持双向数据同步
- 🔄 **批量导入功能**：批量扫描和导入媒体文件
- 🔄 **搜索和过滤**：高级搜索和筛选功能

### 未来规划
- 📋 **Web 前端界面**：基于现代前端框架的管理界面
- 📋 **用户权限管理**：多用户支持和权限控制
- 📋 **媒体流播放**：集成视频播放功能
- 📋 **自动元数据获取**：从在线数据库获取电影信息

## 版权信息

© 2025 KleinerSource. All rights reserved.