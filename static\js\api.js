/**
 * API 客户端模块
 * 处理与后端 API 的所有交互
 */

class ApiClient {
    constructor() {
        this.baseURL = window.location.origin + '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
        this.timeout = 30000; // 30秒超时
        this.retries = 3;
    }

    /**
     * 发送 HTTP 请求
     * @param {string} endpoint - API 端点
     * @param {Object} options - 请求选项
     * @returns {Promise} 响应数据
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            signal: AbortSignal.timeout(this.timeout),
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`API 请求失败 [${endpoint}]:`, error);
            throw error;
        }
    }

    /**
     * 带重试的请求
     * @param {Function} requestFn - 请求函数
     * @param {number} maxRetries - 最大重试次数
     * @returns {Promise} 响应数据
     */
    async requestWithRetry(requestFn, maxRetries = this.retries) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await requestFn();
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                
                const delay = Math.pow(2, i) * 1000; // 指数退避
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    /**
     * GET 请求
     * @param {string} endpoint - API 端点
     * @param {Object} params - 查询参数
     * @returns {Promise} 响应数据
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    /**
     * POST 请求
     * @param {string} endpoint - API 端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 响应数据
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT 请求
     * @param {string} endpoint - API 端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 响应数据
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE 请求
     * @param {string} endpoint - API 端点
     * @param {Object} data - 请求数据（可选）
     * @returns {Promise} 响应数据
     */
    async delete(endpoint, data = null) {
        const options = { method: 'DELETE' };
        if (data) {
            options.body = JSON.stringify(data);
        }
        return this.request(endpoint, options);
    }

    // ========== 影片相关 API ==========

    /**
     * 获取影片列表
     * @param {Object} params - 过滤参数
     * @returns {Promise} 影片列表数据
     */
    async getMovies(params = {}) {
        return this.post('/movies', params);
    }

    /**
     * 获取影片详情
     * @param {number} movieId - 影片 ID
     * @returns {Promise} 影片详情数据
     */
    async getMovie(movieId) {
        return this.get(`/movies/id/${movieId}`);
    }

    /**
     * 更新影片信息
     * @param {number} movieId - 影片 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateMovie(movieId, data) {
        return this.put(`/movies/${movieId}`, data);
    }

    /**
     * 删除影片
     * @param {number} movieId - 影片 ID
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteMovie(movieId, force = false) {
        const params = force ? { force: true } : {};
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/movies/${movieId}?${queryString}` : `/movies/${movieId}`;
        return this.delete(endpoint);
    }

    // ========== 标签相关 API ==========

    /**
     * 获取标签列表
     * @returns {Promise} 标签列表数据
     */
    async getTags() {
        return this.get('/tags');
    }

    /**
     * 创建标签
     * @param {Object} data - 标签数据
     * @returns {Promise} 创建结果
     */
    async createTag(data) {
        return this.post('/tags', data);
    }

    /**
     * 更新标签
     * @param {number} tagId - 标签 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateTag(tagId, data) {
        return this.put(`/tags/${tagId}`, data);
    }

    /**
     * 删除标签
     * @param {number|Array<number>} ids - 标签 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteTag(ids, force = false) {
        return this.delete('/tags', {
            ids: ids,
            force: force
        });
    }

    // ========== 分类相关 API ==========

    /**
     * 获取分类列表
     * @returns {Promise} 分类列表数据
     */
    async getGenres() {
        return this.get('/genres');
    }

    /**
     * 创建分类
     * @param {Object} data - 分类数据
     * @returns {Promise} 创建结果
     */
    async createGenre(data) {
        return this.post('/genres', data);
    }

    /**
     * 更新分类
     * @param {number} genreId - 分类 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateGenre(genreId, data) {
        return this.put(`/genres/${genreId}`, data);
    }

    /**
     * 删除分类
     * @param {number|Array<number>} ids - 分类 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteGenre(ids, force = false) {
        return this.delete('/genres', {
            ids: ids,
            force: force
        });
    }

    // ========== 系列相关 API ==========

    /**
     * 获取系列列表
     * @returns {Promise} 系列列表数据
     */
    async getSeries() {
        return this.get('/series');
    }

    /**
     * 创建系列
     * @param {Object} data - 系列数据
     * @returns {Promise} 创建结果
     */
    async createSeries(data) {
        return this.post('/series', data);
    }

    /**
     * 更新系列
     * @param {number} seriesId - 系列 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateSeries(seriesId, data) {
        return this.put(`/series/${seriesId}`, data);
    }

    /**
     * 删除系列
     * @param {number|Array<number>} ids - 系列 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteSeries(ids, force = false) {
        return this.delete('/series', {
            ids: ids,
            force: force
        });
    }

    // ========== 演员相关 API ==========

    /**
     * 获取演员列表
     * @returns {Promise} 演员列表数据
     */
    async getActors() {
        return this.get('/actors');
    }

    /**
     * 创建演员
     * @param {Object} data - 演员数据
     * @returns {Promise} 创建结果
     */
    async createActors(data) {
        return this.post('/actors', data);
    }

    /**
     * 更新演员
     * @param {number} actorsId - 演员 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateActors(actorsId, data) {
        return this.put(`/actors/${actorsId}`, data);
    }

    /**
     * 删除演员
     * @param {number|Array<number>} ids - 演员 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteActors(ids, force = false) {
        return this.delete('/actors', {
            ids: ids,
            force: force
        });
    }

    // ========== 目录相关 API ==========

    /**
     * 获取目录列表
     * @param {boolean} enabledOnly - 是否只获取启用的目录
     * @param {boolean} withCover - 是否包含封面图片
     * @returns {Promise} 目录列表数据
     */
    async getDirectories(enabledOnly = false, withCover = false) {
        const params = {};
        if (enabledOnly) params.enabled_only = true;
        if (withCover) params.with_cover = true;
        return this.get('/directories', params);
    }

    /**
     * 创建目录
     * @param {Object} data - 目录数据
     * @returns {Promise} 创建结果
     */
    async createDirectory(data) {
        return this.post('/directories', data);
    }

    /**
     * 更新目录
     * @param {number} directoryId - 目录 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateDirectory(directoryId, data) {
        return this.put(`/directories/${directoryId}`, data);
    }

    /**
     * 删除目录
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 删除结果
     */
    async deleteDirectory(directoryId) {
        return this.delete(`/directories/${directoryId}`);
    }

    /**
     * 扫描目录（增量扫描）
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 扫描结果
     */
    async scanDirectory(directoryId) {
        return this.post(`/directories/${directoryId}/scan`, { incremental: true });
    }

    /**
     * 全量扫描目录
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 扫描结果
     */
    async fullScanDirectory(directoryId) {
        return this.post(`/directories/${directoryId}/scan`, { incremental: false });
    }

    /**
     * 重新生成所有目录封面
     * @returns {Promise} 生成结果
     */
    async regenerateAllCovers() {
        return this.post('/directories/regenerate-all-covers');
    }

    // ========== 图片相关 API ==========

    /**
     * 获取图片 URL
     * @param {string} imageUuid - 图片 UUID
     * @returns {string} 图片 URL
     */
    getImageUrl(imageUuid) {
        if (!imageUuid) return null;
        return `${this.baseURL}/images/${imageUuid}`;
    }

    /**
     * 获取图片信息
     * @param {string} imageUuid - 图片 UUID
     * @returns {Promise} 图片信息
     */
    async getImageInfo(imageUuid) {
        return this.get(`/images/${imageUuid}/info`);
    }

    // ========== 系统相关 API ==========

    /**
     * 获取数据库统计信息
     * @returns {Promise} 统计数据
     */
    async getDatabaseStats() {
        return this.get('/database/stats');
    }

    /**
     * 健康检查
     * @returns {Promise} 健康状态
     */
    async healthCheck() {
        return this.get('/health');
    }

    // ========== 收藏功能 API ==========

    /**
     * 收藏影片
     * @param {number} movieId - 影片ID
     * @returns {Promise} 收藏结果
     */
    async addFavorite(movieId) {
        return this.request(`/favorites/${movieId}`, {
            method: 'POST'
        });
    }

    /**
     * 取消收藏影片
     * @param {number} movieId - 影片ID
     * @returns {Promise} 取消收藏结果
     */
    async removeFavorite(movieId) {
        return this.request(`/favorites/${movieId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 切换收藏状态
     * @param {number} movieId - 影片ID
     * @returns {Promise} 切换结果
     */
    async toggleFavorite(movieId) {
        return this.request(`/favorites/${movieId}/toggle`, {
            method: 'PUT'
        });
    }

    /**
     * 获取收藏状态
     * @param {number} movieId - 影片ID
     * @returns {Promise} 收藏状态
     */
    async getFavoriteStatus(movieId) {
        return this.request(`/favorites/${movieId}/status`);
    }

    /**
     * 获取收藏列表
     * @param {Object} filterParams - 过滤参数
     * @returns {Promise} 收藏列表
     */
    async getFavorites(filterParams = {}) {
        return this.request('/favorites', {
            method: 'POST',
            body: JSON.stringify(filterParams)
        });
    }

    /**
     * 批量收藏影片
     * @param {number[]} movieIds - 影片ID列表
     * @returns {Promise} 批量收藏结果
     */
    async batchAddFavorites(movieIds) {
        return this.request('/favorites/batch', {
            method: 'POST',
            body: JSON.stringify({ movie_ids: movieIds })
        });
    }

    /**
     * 批量移除收藏影片
     * @param {number[]} movieIds - 影片ID列表
     * @returns {Promise} 批量移除收藏结果
     */
    async batchRemoveFavorites(movieIds) {
        return this.request('/favorites/batch', {
            method: 'DELETE',
            body: JSON.stringify({ movie_ids: movieIds })
        });
    }



    // ========== 映射管理 API ==========

    /**
     * 获取映射规则列表
     * @param {string} mappingType - 映射类型 (tags/genres/actors)
     * @param {Object} params - 查询参数
     * @returns {Promise} 映射规则列表
     */
    async getMappingRules(mappingType, params = {}) {
        const queryParams = new URLSearchParams(params);
        return this.request(`/mappings/${mappingType}?${queryParams}`);
    }

    /**
     * 创建映射规则
     * @param {string} mappingType - 映射类型
     * @param {Object} data - 映射规则数据
     * @returns {Promise} 创建结果
     */
    async createMappingRule(mappingType, data) {
        return this.request(`/mappings/${mappingType}`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * 更新映射规则
     * @param {string} mappingType - 映射类型
     * @param {number} ruleId - 规则ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateMappingRule(mappingType, ruleId, data) {
        return this.request(`/mappings/${mappingType}/${ruleId}`, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * 删除映射规则
     * @param {string} mappingType - 映射类型
     * @param {number} ruleId - 规则ID
     * @returns {Promise} 删除结果
     */
    async deleteMappingRule(mappingType, ruleId) {
        return this.request(`/mappings/${mappingType}/${ruleId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 批量创建映射规则
     * @param {string} mappingType - 映射类型
     * @param {Object} data - 批量数据
     * @returns {Promise} 批量创建结果
     */
    async batchCreateMappingRules(mappingType, data) {
        return this.request(`/mappings/${mappingType}/batch`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * 导出映射规则
     * @param {string} mappingType - 映射类型
     * @returns {Promise} 导出数据
     */
    async exportMappingRules(mappingType) {
        return this.request(`/mappings/${mappingType}/export`);
    }
}

// 创建全局 API 客户端实例
const api = new ApiClient();

// 错误处理工具函数
const handleApiError = (error, context = '', showToast = true) => {
    console.error(`API 错误 ${context}:`, error);

    let message = '操作失败，请稍后重试';

    if (error.message) {
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
            message = '网络连接失败，请检查网络连接';
        } else if (error.message.includes('404')) {
            message = '请求的资源不存在';
        } else if (error.message.includes('500')) {
            message = '服务器内部错误';
        } else if (error.message.includes('timeout')) {
            message = '请求超时，请稍后重试';
        } else {
            message = error.message;
        }
    }

    // 自动显示错误Toast（除非明确禁用）
    if (showToast && window.toast) {
        window.toast.error(message);
    }

    return message;
};

// 导出 API 客户端和工具函数
window.api = api;
window.handleApiError = handleApiError;
