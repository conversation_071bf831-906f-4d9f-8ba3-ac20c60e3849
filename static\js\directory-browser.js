/**
 * 目录浏览器组件
 * 用于浏览服务器端目录结构并选择路径
 */
class DirectoryBrowser {
    constructor() {
        this.currentPath = '/';
        this.selectedPath = '';
        this.isLoading = false;
        this.init();
    }

    /**
     * 初始化目录浏览器
     */
    init() {
        this.bindEvents();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 浏览按钮点击事件
        document.getElementById('browse-path-btn')?.addEventListener('click', () => {
            this.openBrowser();
        });

        // 确认选择按钮
        document.getElementById('confirm-path-selection')?.addEventListener('click', () => {
            this.confirmSelection();
        });

        // 重试按钮
        document.getElementById('retry-load-directories')?.addEventListener('click', () => {
            this.loadDirectories(this.currentPath);
        });

        // 模态框关闭事件
        document.getElementById('directory-browser-modal')?.addEventListener('close', () => {
            this.resetBrowser();
        });
    }

    /**
     * 打开目录浏览器
     */
    async openBrowser(initialPath = '') {
        const modal = document.getElementById('directory-browser-modal');
        if (!modal) {
            console.error('找不到目录浏览器模态框');
            return;
        }

        // 重置状态
        this.resetBrowser();
        this.currentPath = initialPath;
        this.selectedPath = '';

        // 显示模态框
        modal.showModal();

        // 加载初始目录（空路径表示根目录列表）
        await this.loadDirectories(initialPath);
    }

    /**
     * 重置浏览器状态
     */
    resetBrowser() {
        this.selectedPath = '';
        this.updateSelectedPathDisplay('');
        this.updateConfirmButton(false);
        this.hideError();
    }

    /**
     * 加载指定路径的目录列表
     */
    async loadDirectories(path) {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();
        this.hideError();

        try {
            const requestBody = {};
            if (path) {
                requestBody.path = path;
            }

            const response = await fetch('/api/directories/browse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                this.currentPath = result.data.current_path;
                this.renderDirectoryList(result.data);
                this.renderBreadcrumbs(result.data.breadcrumbs);
            } else {
                throw new Error('无效的响应数据');
            }

        } catch (error) {
            console.error('加载目录失败:', error);
            this.showError(error.message || '加载目录失败');
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    /**
     * 渲染目录列表
     */
    renderDirectoryList(data) {
        const container = document.getElementById('directory-list');
        if (!container) return;

        container.innerHTML = '';

        // 添加返回上级目录选项
        if (data.parent_path) {
            const parentItem = this.createDirectoryItem({
                name: '..',
                path: data.parent_path,
                is_directory: true,
                is_parent: true
            });
            container.appendChild(parentItem);
        }

        // 添加子目录
        data.items.forEach(item => {
            if (item.is_directory) {
                const dirItem = this.createDirectoryItem(item);
                container.appendChild(dirItem);
            }
        });

        // 如果没有子目录，显示提示
        if (data.items.length === 0 && !data.parent_path) {
            const emptyItem = document.createElement('div');
            emptyItem.className = 'text-center py-8 text-base-content/60';
            emptyItem.innerHTML = `
                <i class="bi bi-folder-x mx-auto mb-3 opacity-50" style="font-size: 3rem;" aria-label="空目录图标"></i>
                <p>此目录下没有子目录</p>
            `;
            container.appendChild(emptyItem);
        }
    }

    /**
     * 创建目录项元素
     */
    createDirectoryItem(item) {
        const div = document.createElement('div');
        div.className = `flex items-center p-3 hover:bg-base-200 cursor-pointer rounded-lg transition-colors ${
            item.is_parent ? 'border-b border-base-300 mb-2' : ''
        }`;
        
        const isParent = item.is_parent;
        const iconClass = isParent ? 'text-warning' : 'text-primary';
        
        div.innerHTML = `
            <div class="flex items-center flex-1 min-w-0">
                <div class="flex-shrink-0 mr-3">
                    <i class="bi ${isParent ? 'bi-arrow-left' : 'bi-folder'} ${iconClass}" aria-label="${isParent ? '返回上级图标' : '目录图标'}"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="font-medium truncate" title="${item.name}">${item.name}</p>
                    ${!isParent ? `<p class="text-xs text-base-content/60 truncate" title="${item.path}">${item.path}</p>` : ''}
                </div>
            </div>
            ${!isParent ? `
                <div class="flex-shrink-0 ml-3">
                    <button type="button" class="btn btn-xs btn-outline select-dir-btn" data-path="${item.path}">
                        选择
                    </button>
                </div>
            ` : ''}
        `;

        // 绑定点击事件
        div.addEventListener('click', (e) => {
            // 如果点击的是选择按钮，不触发目录导航
            if (e.target.classList.contains('select-dir-btn')) {
                e.stopPropagation();
                this.selectDirectory(item.path);
                return;
            }
            
            // 双击进入目录
            this.navigateToDirectory(item.path);
        });

        // 绑定选择按钮事件
        const selectBtn = div.querySelector('.select-dir-btn');
        if (selectBtn) {
            selectBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectDirectory(item.path);
            });
        }

        return div;
    }

    /**
     * 导航到指定目录
     */
    async navigateToDirectory(path) {
        await this.loadDirectories(path);
    }

    /**
     * 选择目录
     */
    selectDirectory(path) {
        this.selectedPath = path;
        this.updateSelectedPathDisplay(path);
        this.updateConfirmButton(true);
    }

    /**
     * 渲染面包屑导航
     */
    renderBreadcrumbs(breadcrumbs) {
        const container = document.getElementById('directory-breadcrumbs');
        if (!container) return;

        container.innerHTML = '';

        breadcrumbs.forEach((crumb) => {
            const li = document.createElement('li');
            
            if (crumb.is_current) {
                li.innerHTML = `<span class="font-medium">${crumb.name}</span>`;
            } else {
                li.innerHTML = `<a href="#" class="hover:text-primary" data-path="${crumb.path}">${crumb.name}</a>`;
                
                // 绑定点击事件
                const link = li.querySelector('a');
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.navigateToDirectory(crumb.path);
                });
            }
            
            container.appendChild(li);
        });
    }

    /**
     * 更新选中路径显示
     */
    updateSelectedPathDisplay(path) {
        const display = document.getElementById('selected-path-display');
        if (display) {
            display.value = path;
        }
    }

    /**
     * 更新确认按钮状态
     */
    updateConfirmButton(enabled) {
        const button = document.getElementById('confirm-path-selection');
        if (button) {
            button.disabled = !enabled;
        }
    }

    /**
     * 确认选择
     */
    confirmSelection() {
        if (!this.selectedPath) {
            if (window.toast) {
                window.toast.warning('请先选择一个目录');
            }
            return;
        }

        // 将选中的路径填入主表单
        const pathInput = document.getElementById('directory-path');
        if (pathInput) {
            pathInput.value = this.selectedPath;
            
            // 触发输入事件以便表单验证
            pathInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // 关闭模态框
        const modal = document.getElementById('directory-browser-modal');
        if (modal) {
            modal.close();
        }

        if (window.toast) {
            window.toast.success('路径选择成功');
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loading = document.getElementById('directory-browser-loading');
        const listContainer = document.getElementById('directory-list-container');
        
        if (loading) loading.classList.remove('hidden');
        if (listContainer) listContainer.classList.add('hidden');
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loading = document.getElementById('directory-browser-loading');
        const listContainer = document.getElementById('directory-list-container');
        
        if (loading) loading.classList.add('hidden');
        if (listContainer) listContainer.classList.remove('hidden');
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const error = document.getElementById('directory-browser-error');
        const errorMessage = document.getElementById('directory-browser-error-message');
        const listContainer = document.getElementById('directory-list-container');
        
        if (error) error.classList.remove('hidden');
        if (errorMessage) errorMessage.textContent = message;
        if (listContainer) listContainer.classList.add('hidden');
    }

    /**
     * 隐藏错误信息
     */
    hideError() {
        const error = document.getElementById('directory-browser-error');
        if (error) error.classList.add('hidden');
    }
}

// 页面加载完成后初始化目录浏览器
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('directory-browser-modal')) {
        window.directoryBrowser = new DirectoryBrowser();
    }
});
