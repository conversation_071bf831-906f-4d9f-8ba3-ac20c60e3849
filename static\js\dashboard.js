/**
 * 仪表板页面 JavaScript
 * 处理统计数据加载、最近影片展示等功能
 */

class DashboardManager {
    constructor() {
        this.api = window.api; // 使用全局 API 客户端实例
        this.isLoading = false;

        // 确保API客户端已初始化
        if (!this.api) {
            return;
        }

        this.init();
    }

    /**
     * 初始化仪表板
     */
    init() {
        // 绑定事件
        this.bindEvents();
        
        // 加载数据
        this.loadDashboardData();
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-dashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshDashboard();
            });
        }
    }

    /**
     * 加载仪表板数据
     */
    async loadDashboardData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            // 并行加载统计数据、目录和最近影片
            await Promise.all([
                this.loadStatistics(),
                this.loadDirectories(),
                this.loadRecentMovies()
            ]);



        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            if (window.toast) {
                window.toast.error('加载数据失败');
            }
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            const response = await this.api.getDatabaseStats();

            if (response.success && response.stats) {
                const stats = response.stats;

                // 更新统计卡片
                this.updateStatCard('total-movies', stats.total_movies || 0);
                this.updateStatCard('total-tags', stats.total_tags || 0);
                this.updateStatCard('total-genres', stats.total_genres || 0);
                this.updateStatCard('total-actors', stats.total_actors || 0);


            } else {
                throw new Error('统计数据格式错误');
            }

        } catch (error) {
            console.error('加载统计数据失败:', error);

            // 显示错误状态
            this.updateStatCard('total-movies', '错误');
            this.updateStatCard('total-tags', '错误');
            this.updateStatCard('total-genres', '错误');
            this.updateStatCard('total-actors', '错误');
        }
    }

    /**
     * 加载最近添加的影片
     */
    async loadRecentMovies() {
        const loadingEl = document.getElementById('recent-movies-loading');
        const gridEl = document.getElementById('recent-movies-grid');
        const emptyEl = document.getElementById('recent-movies-empty');
        
        try {
            // 显示加载状态
            loadingEl?.classList.remove('hidden');
            gridEl?.classList.add('hidden');
            emptyEl?.classList.add('hidden');
            
            // 获取最近添加的影片（按创建时间倒序，限制12部）
            const response = await this.api.getMovies({
                limit: 12,
                offset: 0,
                sort_by: 'created_at',
                sort_order: 'desc'
            });
            
            if (response.success && response.data) {
                const movies = response.data;
                
                if (movies.length > 0) {
                    this.renderRecentMovies(movies);
                    gridEl?.classList.remove('hidden');
                } else {
                    emptyEl?.classList.remove('hidden');
                }
                

            } else {
                throw new Error('影片数据格式错误');
            }
            
        } catch (error) {
            console.error('加载最近影片失败:', error);
            emptyEl?.classList.remove('hidden');
        } finally {
            loadingEl?.classList.add('hidden');
        }
    }

    /**
     * 渲染最近影片网格
     */
    renderRecentMovies(movies) {
        const gridEl = document.getElementById('recent-movies-grid');
        if (!gridEl) return;
        
        gridEl.innerHTML = '';
        
        movies.forEach(movie => {
            const movieCard = this.createMovieCard(movie);
            gridEl.appendChild(movieCard);
        });
    }

    /**
     * 创建影片卡片元素
     */
    createMovieCard(movie) {
        const card = document.createElement('div');
        card.className = 'group cursor-pointer';
        
        // 构建海报 URL
        const posterUrl = movie.poster_uuid
            ? `/api/images/${movie.poster_uuid}`
            : '/static/images/no-poster.svg';
        
        // 格式化年份
        const year = movie.year ? `(${movie.year})` : '';
        
        // 格式化评分
        const rating = movie.rating ? `★ ${movie.rating.toFixed(1)}` : '';
        
        card.innerHTML = `
            <div class="card bg-base-100 shadow-sm border border-base-300 hover:shadow-lg transition-all duration-200 group-hover:scale-105">
                <figure class="aspect-[2/3] bg-base-200">
                    <img 
                        src="${posterUrl}" 
                        alt="${movie.title}"
                        class="w-full h-full object-cover"
                        loading="lazy"
                        onerror="this.src='/static/images/no-poster.svg'"
                    />
                </figure>
                <div class="card-body p-3">
                    <h3 class="card-title text-sm line-clamp-2 min-h-[2.5rem]" title="${movie.title}">
                        ${movie.title}
                    </h3>
                    <div class="flex flex-col gap-1 text-xs text-base-content/70">
                        ${year ? `<span>${year}</span>` : ''}
                        ${rating ? `<span class="text-warning">${rating}</span>` : ''}
                    </div>
                </div>
            </div>
        `;
        
        // 添加点击事件
        card.addEventListener('click', () => {
            if (movie.num) {
                window.location.href = `/movies/${movie.num}`;
            } else {
                window.location.href = `/movies/id/${movie.id}`;
            }
        });
        
        return card;
    }

    /**
     * 更新统计卡片数值
     */
    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            // 添加动画效果
            element.style.opacity = '0.5';
            setTimeout(() => {
                element.textContent = this.formatNumber(value);
                element.style.opacity = '1';
            }, 150);
        }
    }

    /**
     * 格式化数字显示
     */
    formatNumber(value) {
        if (typeof value !== 'number') {
            return value;
        }
        
        if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
        } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
        } else {
            return value.toString();
        }
    }

    /**
     * 加载目录数据
     */
    async loadDirectories() {
        try {
            const response = await this.api.getDirectories(true, true); // 获取启用的目录和封面

            if (response.success && response.data) {
                this.updateDirectoriesDisplay(response.data);
            } else {
                throw new Error('目录数据格式错误');
            }

        } catch (error) {
            this.showDirectoriesError();
        }
    }

    /**
     * 更新目录显示
     * @param {Array} directories - 目录列表
     */
    updateDirectoriesDisplay(directories) {
        const container = document.getElementById('directories-grid');
        const loading = document.getElementById('directories-loading');
        const empty = document.getElementById('directories-empty');

        if (!container || !loading || !empty) return;

        // 隐藏加载状态
        loading.classList.add('hidden');

        if (directories.length === 0) {
            // 显示空状态
            container.classList.add('hidden');
            empty.classList.remove('hidden');
            return;
        }

        // 显示目录卡片
        empty.classList.add('hidden');
        container.classList.remove('hidden');

        const directoriesHtml = directories.map(directory => this.createDirectoryCard(directory)).join('');
        container.innerHTML = directoriesHtml;

        // 绑定目录卡片点击事件
        this.bindDirectoryCardEvents();
    }

    /**
     * 创建目录卡片 HTML
     * @param {Object} directory - 目录对象
     * @returns {string} HTML 字符串
     */
    createDirectoryCard(directory) {
        // 生成封面图片HTML
        const coverImageHtml = directory.cover_image_base64
            ? `<img src="${directory.cover_image_base64}" alt="${directory.name}" class="w-full h-full object-cover" loading="lazy">`
            : `<div class="w-full h-full bg-base-300 flex items-center justify-center">
                <i class="bi bi-folder text-base-content/30" style="font-size: 4rem;" aria-label="默认目录图标"></i>
               </div>`;

        return `
            <div class="card bg-base-100 border border-base-300 hover:border-primary/50 hover:shadow-lg transition-all duration-200 cursor-pointer directory-card"
                 data-directory-id="${directory.id}" data-directory-path="${directory.path}">
                <!-- 封面图片区域 (保持350:197宽高比，占卡片主要部分) -->
                <figure class="relative w-full overflow-hidden rounded-t-lg" style="aspect-ratio: 350/197;">
                    ${coverImageHtml}

                    <!-- 影片数量角标 (右上角) -->
                    <div class="absolute top-2 right-2">
                        <div class="badge badge-primary badge-sm font-medium">
                            ${directory.file_count || 0}
                        </div>
                    </div>

                    <!-- 悬停遮罩 -->
                    <div class="absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
                        <div class="badge badge-primary badge-sm">
                            <i class="bi bi-arrow-right mr-1" aria-label="查看图标"></i>
                            查看影片
                        </div>
                    </div>
                </figure>

                <!-- 目录信息区域 (紧凑设计，只显示目录名称) -->
                <div class="card-body p-2 py-2.5">
                    <div class="flex items-center justify-center">
                        <h3 class="card-title text-sm line-clamp-1 text-center" title="${directory.name}">
                            ${directory.name}
                        </h3>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定目录卡片点击事件
     */
    bindDirectoryCardEvents() {
        const directoryCards = document.querySelectorAll('.directory-card');
        directoryCards.forEach(card => {
            card.addEventListener('click', () => {
                const directoryId = card.getAttribute('data-directory-id');

                if (directoryId) {
                    // 跳转到影片库页面并传递目录筛选参数
                    const url = new URL('/movies', window.location.origin);
                    url.searchParams.set('directory_id', directoryId);
                    window.location.href = url.toString();
                }
            });
        });
    }

    /**
     * 显示目录加载错误
     */
    showDirectoriesError() {
        const container = document.getElementById('directories-grid');
        const loading = document.getElementById('directories-loading');
        const empty = document.getElementById('directories-empty');

        if (!container || !loading || !empty) return;

        loading.classList.add('hidden');
        container.classList.add('hidden');

        // 修改空状态显示错误信息
        empty.classList.remove('hidden');
        const emptyText = empty.querySelector('p');
        if (emptyText) {
            emptyText.textContent = '加载目录数据失败';
        }
    }

    /**
     * 刷新仪表板
     */
    async refreshDashboard() {
        const refreshBtn = document.getElementById('refresh-dashboard');
        
        // 添加加载状态
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = `
                <span class="loading loading-spinner loading-xs"></span>
                刷新中...
            `;
        }
        
        try {
            await this.loadDashboardData();
            if (window.toast) {
                window.toast.success('数据刷新成功');
            }
        } catch (error) {
            console.error('刷新失败:', error);
            if (window.toast) {
                window.toast.error('刷新失败');
            }
        } finally {
            // 恢复按钮状态
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = `
                    <i class="bi bi-arrow-clockwise" aria-label="刷新图标"></i>
                    刷新
                `;
            }
        }
    }
}

// 页面加载完成后初始化仪表板
document.addEventListener('DOMContentLoaded', function() {
    // 确保API客户端已加载
    if (typeof window.api === 'undefined') {
        setTimeout(() => {
            if (typeof window.api !== 'undefined') {
                window.dashboardManager = new DashboardManager();
            }
        }, 100);
    } else {
        window.dashboardManager = new DashboardManager();
    }
});

// 添加 CSS 样式用于文本截断
const style = document.createElement('style');
style.textContent = `
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
`;
document.head.appendChild(style);
