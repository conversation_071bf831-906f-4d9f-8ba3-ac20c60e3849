/**
 * DaisyUI v5 原生主题管理器
 * 使用 DaisyUI 官方主题切换机制，支持多主题选择
 */

class ThemeManager {
    constructor() {
        // 支持的主题列表
        this.themes = [
            { id: 'light', name: '浅色', icon: 'sun' },
            { id: 'dark', name: '深色', icon: 'moon' },
            { id: 'cupcake', name: '纸杯蛋糕', icon: 'cake' },
            { id: 'synthwave', name: '合成波', icon: 'wave' },
            { id: 'retro', name: '复古', icon: 'retro' },
            { id: 'cyberpunk', name: '赛博朋克', icon: 'cyber' },
            { id: 'valentine', name: '情人节', icon: 'heart' },
            { id: 'halloween', name: '万圣节', icon: 'ghost' },
            { id: 'garden', name: '花园', icon: 'flower' },
            { id: 'forest', name: '森林', icon: 'tree' },
            { id: 'aqua', name: '水蓝', icon: 'water' },
            { id: 'lofi', name: 'Lo-Fi', icon: 'music' },
            { id: 'pastel', name: '粉彩', icon: 'palette' },
            { id: 'fantasy', name: '幻想', icon: 'star' },
            { id: 'wireframe', name: '线框', icon: 'grid' },
            { id: 'black', name: '纯黑', icon: 'circle' },
            { id: 'luxury', name: '奢华', icon: 'diamond' },
            { id: 'dracula', name: '德古拉', icon: 'bat' },
            { id: 'cmyk', name: 'CMYK', icon: 'print' },
            { id: 'autumn', name: '秋天', icon: 'leaf' },
            { id: 'business', name: '商务', icon: 'briefcase' },
            { id: 'acid', name: '酸性', icon: 'drop' },
            { id: 'lemonade', name: '柠檬水', icon: 'lemon' },
            { id: 'night', name: '夜晚', icon: 'moon-stars' },
            { id: 'coffee', name: '咖啡', icon: 'coffee' },
            { id: 'winter', name: '冬天', icon: 'snowflake' },
            { id: 'dim', name: '暗淡', icon: 'dim' },
            { id: 'nord', name: '北欧', icon: 'mountain' },
            { id: 'sunset', name: '日落', icon: 'sunset' }
        ];
        
        this.currentTheme = 'light';
        this.storageKey = 'daisyui-theme';
        
        this.init();
    }

    /**
     * 初始化主题管理器
     */
    init() {
        // 从 localStorage 获取保存的主题
        const savedTheme = localStorage.getItem(this.storageKey);
        
        if (savedTheme && this.isValidTheme(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            // 如果没有保存的主题，使用系统偏好
            this.currentTheme = this.getSystemPreferredTheme();
        }
        
        // 立即应用主题，避免闪烁
        this.applyTheme(this.currentTheme);
        
        // 等待 DOM 加载完成后初始化 UI
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initUI());
        } else {
            this.initUI();
        }
    }

    /**
     * 初始化主题选择器 UI
     */
    initUI() {
        this.createThemeSelector();
        this.bindEvents();
        this.updateThemeIndicator();
    }

    /**
     * 创建主题选择器
     */
    createThemeSelector() {
        const themeGrid = document.getElementById('theme-grid');
        if (!themeGrid) return;

        // 清空现有内容
        themeGrid.innerHTML = '';

        // 添加主题选项
        this.themes.forEach(theme => {
            const themeCard = document.createElement('div');
            themeCard.className = 'theme-option relative';
            themeCard.innerHTML = `
                <button class="w-full p-3 rounded-lg border-2 border-base-300 hover:border-primary transition-all duration-200 group"
                        data-theme="${theme.id}">
                    <!-- 主题预览色块 -->
                    <div class="flex gap-1 mb-2" data-theme="${theme.id}">
                        <div class="w-3 h-3 rounded-full bg-primary"></div>
                        <div class="w-3 h-3 rounded-full bg-secondary"></div>
                        <div class="w-3 h-3 rounded-full bg-accent"></div>
                        <div class="w-3 h-3 rounded-full bg-neutral"></div>
                    </div>
                    <!-- 主题名称 -->
                    <div class="text-xs font-medium text-base-content group-hover:text-primary transition-colors">
                        ${theme.name}
                    </div>
                    <!-- 选中指示器 -->
                    <div class="absolute top-1 right-1 opacity-0 transition-opacity" data-check>
                        <div class="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-primary-content" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                </button>
            `;
            themeGrid.appendChild(themeCard);
        });
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主题选项点击事件
        document.addEventListener('click', (e) => {
            const themeButton = e.target.closest('.theme-option button');
            if (themeButton) {
                const theme = themeButton.dataset.theme;
                this.setTheme(theme);

                // 关闭下拉菜单
                const dropdown = themeButton.closest('.dropdown');
                if (dropdown) {
                    const trigger = dropdown.querySelector('[tabindex="0"]');
                    if (trigger) trigger.blur();
                }
            }
        });

        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                // 如果当前使用的是系统偏好主题，则更新
                if (!localStorage.getItem(this.storageKey)) {
                    const systemTheme = this.getSystemPreferredTheme();
                    this.applyTheme(systemTheme);
                }
            });
        }

        // 键盘导航支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // ESC 键关闭下拉菜单
                const dropdown = document.querySelector('.theme-dropdown');
                if (dropdown) {
                    const trigger = dropdown.querySelector('[tabindex="0"]');
                    if (trigger) trigger.blur();
                }
            }
        });
    }

    /**
     * 设置主题
     */
    setTheme(theme) {
        if (!this.isValidTheme(theme)) {
            return;
        }

        this.currentTheme = theme;
        this.applyTheme(theme);
        this.saveTheme(theme);
        this.updateThemeIndicator();
        
        // 触发主题变化事件
        this.dispatchThemeChangeEvent(theme);
    }

    /**
     * 应用主题
     */
    applyTheme(theme) {
        // 使用 DaisyUI 原生方式设置主题
        document.documentElement.setAttribute('data-theme', theme);
        
        // 添加过渡效果
        document.documentElement.style.transition = 'color 0.3s ease, background-color 0.3s ease';
        
        // 移除过渡效果（避免影响其他动画）
        setTimeout(() => {
            document.documentElement.style.transition = '';
        }, 300);
    }

    /**
     * 保存主题到 localStorage
     */
    saveTheme(theme) {
        try {
            localStorage.setItem(this.storageKey, theme);
        } catch (error) {
            // 静默处理存储错误
        }
    }

    /**
     * 更新主题指示器
     */
    updateThemeIndicator() {
        // 更新当前主题名称显示
        const currentThemeNameElement = document.getElementById('current-theme-name');
        if (currentThemeNameElement) {
            const currentThemeInfo = this.themes.find(t => t.id === this.currentTheme);
            if (currentThemeInfo) {
                currentThemeNameElement.textContent = currentThemeInfo.name;
            }
        }

        // 更新选中状态
        document.querySelectorAll('.theme-option button').forEach(button => {
            const checkIcon = button.querySelector('[data-check]');
            const themeId = button.dataset.theme;

            if (themeId === this.currentTheme) {
                // 选中状态
                button.classList.add('border-primary', 'bg-primary/10');
                button.classList.remove('border-base-300');
                if (checkIcon) checkIcon.style.opacity = '1';
            } else {
                // 未选中状态
                button.classList.remove('border-primary', 'bg-primary/10');
                button.classList.add('border-base-300');
                if (checkIcon) checkIcon.style.opacity = '0';
            }
        });
    }

    /**
     * 获取系统偏好主题
     */
    getSystemPreferredTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    /**
     * 验证主题是否有效
     */
    isValidTheme(theme) {
        return this.themes.some(t => t.id === theme);
    }

    /**
     * 触发主题变化事件
     */
    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themechange', {
            detail: { theme, themeManager: this }
        });
        document.dispatchEvent(event);
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取所有可用主题
     */
    getAvailableThemes() {
        return [...this.themes];
    }

    /**
     * 切换到下一个主题（用于快捷键等）
     */
    nextTheme() {
        const currentIndex = this.themes.findIndex(t => t.id === this.currentTheme);
        const nextIndex = (currentIndex + 1) % this.themes.length;
        this.setTheme(this.themes[nextIndex].id);
    }

    /**
     * 切换到上一个主题
     */
    previousTheme() {
        const currentIndex = this.themes.findIndex(t => t.id === this.currentTheme);
        const prevIndex = currentIndex === 0 ? this.themes.length - 1 : currentIndex - 1;
        this.setTheme(this.themes[prevIndex].id);
    }
}

// 立即创建主题管理器实例（在 DOM 加载前）
window.themeManager = new ThemeManager();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
