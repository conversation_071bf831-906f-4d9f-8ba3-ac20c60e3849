/**
 * 搜索下拉单选框组件
 * 支持搜索、单选、创建新项目等功能
 */

class SingleSelectDropdown {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            placeholder: '请选择...',
            searchPlaceholder: '搜索...',
            noDataText: '暂无数据',
            noResultsText: '未找到匹配项',
            allowClear: true,
            allowCreate: false,
            createText: '按回车创建',
            createApiUrl: null,
            createApiMethod: 'POST',
            onCreateSuccess: null,
            onCreateError: null,
            onChange: null,
            ...options
        };
        
        this.data = [];
        this.selectedItem = null;
        this.isOpen = false;
        this.searchTerm = '';
        
        this.init();
    }
    
    init() {
        this.render();
        this.bindEvents();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="single-select-dropdown relative">
                <!-- 选择框主体 -->
                <div class="single-select-trigger input input-bordered w-full h-12 cursor-pointer flex items-center justify-between p-2">
                    <div class="single-select-content flex-1">
                        <span class="single-select-placeholder text-base-content/50">${this.options.placeholder}</span>
                    </div>
                    <div class="single-select-actions flex items-center gap-1">
                        <button type="button" class="single-select-clear btn btn-ghost btn-xs btn-circle hidden" title="清除选择">
                            <i class="bi bi-x" style="font-size: 0.75rem;" aria-label="清除选择图标"></i>
                        </button>
                        <div class="single-select-arrow ml-1 flex-shrink-0">
                            <i class="bi bi-chevron-down transition-transform duration-200" aria-label="下拉箭头图标"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 下拉面板 -->
                <div class="single-select-dropdown-panel absolute top-full left-0 right-0 z-50 bg-base-100 border border-base-300 rounded-lg shadow-lg mt-1 hidden max-h-64 overflow-hidden">
                    <!-- 搜索框 -->
                    <div class="p-2 border-b border-base-300">
                        <input type="text" class="single-select-search input input-sm input-bordered w-full" placeholder="${this.options.searchPlaceholder}">
                    </div>
                    
                    <!-- 选项列表 -->
                    <div class="single-select-options-container max-h-48 overflow-y-auto">
                        <!-- 选项将在这里动态生成 -->
                    </div>
                    
                    <!-- 创建提示 -->
                    <div class="single-select-create-hint p-2 border-t border-base-300 text-sm text-base-content/60 hidden">
                        ${this.options.createText}
                    </div>
                </div>
            </div>
        `;
        
        // 缓存DOM元素
        this.trigger = this.container.querySelector('.single-select-trigger');
        this.content = this.container.querySelector('.single-select-content');
        this.placeholder = this.container.querySelector('.single-select-placeholder');
        this.clearBtn = this.container.querySelector('.single-select-clear');
        this.arrow = this.container.querySelector('.single-select-arrow i');
        this.panel = this.container.querySelector('.single-select-dropdown-panel');
        this.searchInput = this.container.querySelector('.single-select-search');
        this.optionsContainer = this.container.querySelector('.single-select-options-container');
        this.createHint = this.container.querySelector('.single-select-create-hint');
    }
    
    bindEvents() {
        // 点击触发器
        this.trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggle();
        });
        
        // 清除按钮
        this.clearBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.clear();
        });
        
        // 搜索输入
        this.searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.renderOptions();
        });

        // 回车键创建新选项
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.handleCreateNewItem();
            }
        });
        
        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });
        
        // 阻止面板点击事件冒泡
        this.panel.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }
    
    setData(data) {
        this.data = data || [];
        this.renderOptions();
    }
    
    renderOptions() {
        if (!this.data.length) {
            this.optionsContainer.innerHTML = `<div class="p-3 text-center text-base-content/60">${this.options.noDataText}</div>`;
            this.updateCreateHint();
            return;
        }
        
        // 过滤数据
        const filteredData = this.data.filter(item => 
            item.name.toLowerCase().includes(this.searchTerm)
        );
        
        if (!filteredData.length) {
            this.optionsContainer.innerHTML = `<div class="p-3 text-center text-base-content/60">${this.options.noResultsText}</div>`;
            this.updateCreateHint();
            return;
        }
        
        const html = filteredData.map(item => {
            const isSelected = this.selectedItem && this.selectedItem.id == item.id;
            
            return `
                <div class="single-select-option flex items-center p-2 hover:bg-base-200 cursor-pointer ${isSelected ? 'bg-primary/10' : ''}"
                     data-value="${item.id}" data-text="${item.name}">
                    <span class="flex-1">${item.name}</span>
                    ${isSelected ? '<i class="bi bi-check text-primary ml-2" aria-label="选中图标"></i>' : ''}
                </div>
            `;
        }).join('');
        
        this.optionsContainer.innerHTML = html;
        this.updateCreateHint();
        
        // 绑定选项点击事件
        this.optionsContainer.querySelectorAll('.single-select-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const value = option.dataset.value;
                const text = option.dataset.text;
                
                this.selectItem({ id: value, name: text });
                this.close();
            });
        });
    }
    
    updateCreateHint() {
        if (this.options.allowCreate && this.searchTerm && 
            !this.data.some(item => item.name.toLowerCase() === this.searchTerm)) {
            this.createHint.classList.remove('hidden');
        } else {
            this.createHint.classList.add('hidden');
        }
    }
    
    selectItem(item) {
        this.selectedItem = item;
        this.renderSelectedItem();
        this.triggerChange();
    }
    
    renderSelectedItem() {
        if (this.selectedItem) {
            this.content.innerHTML = `<span class="text-base-content">${this.selectedItem.name}</span>`;
            this.clearBtn.classList.remove('hidden');
        } else {
            this.content.innerHTML = `<span class="single-select-placeholder text-base-content/50">${this.options.placeholder}</span>`;
            this.clearBtn.classList.add('hidden');
        }
    }
    
    clear() {
        this.selectedItem = null;
        this.renderSelectedItem();
        this.triggerChange();
    }
    
    open() {
        this.isOpen = true;
        this.panel.classList.remove('hidden');
        this.arrow.style.transform = 'rotate(180deg)';
        this.searchInput.focus();
        this.searchInput.value = '';
        this.searchTerm = '';
        this.renderOptions();
    }
    
    close() {
        this.isOpen = false;
        this.panel.classList.add('hidden');
        this.arrow.style.transform = 'rotate(0deg)';
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    async handleCreateNewItem() {
        if (!this.options.allowCreate || !this.searchTerm.trim()) {
            return;
        }
        
        // 检查是否已存在
        if (this.data.some(item => item.name.toLowerCase() === this.searchTerm.toLowerCase())) {
            return;
        }
        
        try {
            const response = await fetch(this.options.createApiUrl, {
                method: this.options.createApiMethod,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: this.searchTerm.trim()
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success && result.data) {
                // 添加到数据列表
                this.data.push(result.data);
                
                // 选择新创建的项目
                this.selectItem(result.data);
                
                // 关闭下拉框
                this.close();
                
                // 触发成功回调
                if (this.options.onCreateSuccess) {
                    this.options.onCreateSuccess(result.data);
                }
            } else {
                throw new Error(result.message || '创建失败');
            }
        } catch (error) {
            console.error('创建新项目失败:', error);
            if (this.options.onCreateError) {
                this.options.onCreateError(error);
            }
        }
    }
    
    triggerChange() {
        if (this.options.onChange) {
            this.options.onChange(this.selectedItem);
        }
    }
    
    // 公共方法
    getValue() {
        return this.selectedItem ? this.selectedItem.id : null;
    }
    
    setValue(value) {
        if (!value) {
            this.clear();
            return;
        }
        
        const item = this.data.find(item => item.id == value);
        if (item) {
            this.selectItem(item);
        }
    }
    
    getSelectedItem() {
        return this.selectedItem;
    }
}
