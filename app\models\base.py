"""
基础模型类和通用字段定义
"""
from datetime import datetime
from sqlalchemy import Column, Integer, DateTime
from sqlalchemy.ext.declarative import declared_attr
from app.core.database import Base


class TimestampMixin:
    """
    时间戳混入类，为模型添加创建时间和更新时间字段
    """
    @declared_attr
    def created_at(cls):
        return Column(DateTime, default=datetime.utcnow, nullable=False)
    
    @declared_attr
    def updated_at(cls):
        return Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class BaseModel(Base, TimestampMixin):
    """
    基础模型类，包含通用字段
    """
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
