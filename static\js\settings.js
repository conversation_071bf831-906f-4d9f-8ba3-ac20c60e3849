/**
 * 设置页面脚本
 * 处理系统设置和映射规则管理
 */

class SettingsManager {
    constructor() {
        // 当前状态
        this.currentTab = 'mappings';
        this.currentMappingType = 'tags';
        this.mappingRules = [];
        this.selectedMappingIds = new Set();
        this.isLoading = false;
        this.searchTimeout = null;
        
        // 分页参数
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        
        // 搜索和过滤参数
        this.searchQuery = '';
        this.statusFilter = '';
        
        // 当前编辑的映射规则
        this.currentMappingRule = null;
        
        // 映射类型配置
        this.mappingTypeConfig = {
            tags: {
                name: '标签',
                icon: 'bi-tags',
                title: '标签映射列表'
            },
            genres: {
                name: '分类',
                icon: 'bi-collection',
                title: '分类映射列表'
            },
            actors: {
                name: '演员',
                icon: 'bi-people',
                title: '演员映射列表'
            }
        };
    }

    /**
     * 初始化设置管理器
     */
    async init() {
        this.bindEvents();
        this.initializeTabs();
        this.initializeBatchDeleteButton();
        await this.loadMappingRules();
    }

    /**
     * 初始化批量删除按钮状态
     */
    initializeBatchDeleteButton() {
        const batchDeleteBtn = document.getElementById('batch-delete-mapping-btn');
        if (batchDeleteBtn) {
            const parentLi = batchDeleteBtn.closest('li');
            if (parentLi) {
                parentLi.style.display = 'none'; // 初始隐藏
            }
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = tab.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // 映射类型切换
        document.querySelectorAll('[data-mapping-type]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const mappingType = btn.getAttribute('data-mapping-type');
                this.switchMappingType(mappingType);
            });
        });

        // 搜索输入
        const searchInput = document.getElementById('mapping-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.searchQuery = e.target.value.trim();
                    this.currentPage = 1;
                    this.loadMappingRules();
                }, 300);
            });
        }

        // 状态过滤
        const statusFilter = document.getElementById('mapping-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.currentPage = 1;
                this.loadMappingRules();
            });
        }

        // 操作按钮
        this.bindMappingButtons();
        this.bindModalEvents();
    }

    /**
     * 绑定映射相关按钮事件
     */
    bindMappingButtons() {
        // 添加映射按钮
        const addBtn = document.getElementById('add-mapping-btn');
        const addFirstBtn = document.getElementById('add-first-mapping-btn');
        if (addBtn) addBtn.addEventListener('click', () => this.showAddMappingModal());
        if (addFirstBtn) addFirstBtn.addEventListener('click', () => this.showAddMappingModal());

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-mapping-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadMappingRules();
            });
        }

        // 导入导出按钮
        const importBtn = document.getElementById('import-mapping-btn');
        const exportBtn = document.getElementById('export-mapping-btn');
        if (importBtn) importBtn.addEventListener('click', () => this.showImportModal());
        if (exportBtn) exportBtn.addEventListener('click', () => this.exportMappingRules());

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batch-delete-mapping-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showBatchDeleteModal();
            });
        }

        // 全选复选框
        const selectAllCheckbox = document.getElementById('mapping-select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        // 映射表单提交
        const mappingForm = document.getElementById('mapping-form');
        if (mappingForm) {
            mappingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleMappingFormSubmit();
            });
        }

        // 删除确认
        const confirmDeleteBtn = document.getElementById('confirm-delete-mapping-btn');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', () => {
                this.handleDeleteMapping();
            });
        }

        // 批量删除确认
        const confirmBatchDeleteBtn = document.getElementById('confirm-batch-delete-mapping-btn');
        if (confirmBatchDeleteBtn) {
            confirmBatchDeleteBtn.addEventListener('click', () => {
                this.handleBatchDeleteMapping();
            });
        }

        // 文件导入
        const fileInput = document.getElementById('mapping-file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e.target.files[0]);
            });
        }

        // 导入提交
        const importSubmitBtn = document.getElementById('import-mapping-submit-btn');
        if (importSubmitBtn) {
            importSubmitBtn.addEventListener('click', () => {
                this.handleImportSubmit();
            });
        }
    }

    /**
     * 初始化标签页
     */
    initializeTabs() {
        // 从URL参数获取初始标签页
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab') || 'mappings';
        this.switchTab(tab);
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新标签页状态
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('tab-active');
            if (tab.getAttribute('data-tab') === tabName) {
                tab.classList.add('tab-active');
            }
        });

        // 显示对应面板
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        const targetPanel = document.getElementById(`${tabName}-panel`);
        if (targetPanel) {
            targetPanel.classList.remove('hidden');
        }

        this.currentTab = tabName;

        // 更新URL参数
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.replaceState({}, '', url);

        // 如果切换到映射标签页，加载数据
        if (tabName === 'mappings') {
            this.loadMappingRules();
        }
    }

    /**
     * 切换映射类型
     */
    switchMappingType(mappingType) {
        // 更新按钮状态
        document.querySelectorAll('[data-mapping-type]').forEach(btn => {
            btn.classList.remove('btn-active');
            if (btn.getAttribute('data-mapping-type') === mappingType) {
                btn.classList.add('btn-active');
            }
        });

        this.currentMappingType = mappingType;
        this.currentPage = 1;
        this.selectedMappingIds.clear();

        // 更新表格标题
        const config = this.mappingTypeConfig[mappingType];
        const tableTitle = document.getElementById('mapping-table-title');
        if (tableTitle && config) {
            tableTitle.textContent = config.title;
        }

        // 重新加载数据
        this.loadMappingRules();
    }

    /**
     * 加载映射规则列表
     */
    async loadMappingRules() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showMappingLoading();
        this.hideMappingStates();

        try {
            const params = {
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize
            };

            // 添加搜索参数
            if (this.searchQuery) {
                params.search = this.searchQuery;
            }

            // 添加状态过滤参数
            if (this.statusFilter) {
                params.status = this.statusFilter;
            }

            const response = await api.getMappingRules(this.currentMappingType, params);
            
            this.mappingRules = response.data || [];
            this.totalCount = response.total_count || 0;

            this.updateMappingDisplay();
            this.updateMappingPagination();
            this.updateMappingStatistics();

        } catch (error) {
            console.error('加载映射规则失败:', error);
            const errorMessage = handleApiError(error, '加载映射规则', false);
            toast.error(errorMessage);
            this.showMappingErrorState(errorMessage);
        } finally {
            this.isLoading = false;
            this.hideMappingLoading();
        }
    }

    /**
     * 更新映射规则显示
     */
    updateMappingDisplay() {
        const tableBody = document.getElementById('mapping-table-body');
        if (!tableBody) return;

        if (this.mappingRules.length === 0) {
            this.showMappingEmptyState();
            return;
        }

        const mappingHtml = this.mappingRules.map(rule => this.createMappingTableRow(rule)).join('');
        tableBody.innerHTML = mappingHtml;

        // 绑定行事件
        this.bindMappingRowEvents();
    }

    /**
     * 创建映射表格行
     */
    createMappingTableRow(rule) {
        const isSelected = this.selectedMappingIds.has(rule.id);

        // 根据业务逻辑更新状态显示：
        // - 有映射值：转换映射
        // - 无映射值：删除原始值
        let statusBadge, mappedValue;
        if (rule.mapped_value) {
            statusBadge = '<span class="badge badge-success">转换映射</span>';
            mappedValue = `<span class="font-medium">${utils.escapeHtml(rule.mapped_value)}</span>`;
        } else {
            statusBadge = '<span class="badge badge-error">删除原值</span>';
            mappedValue = '<span class="text-error italic">删除此原始值</span>';
        }

        return `
            <tr class="hover">
                <td>
                    <input type="checkbox" class="checkbox checkbox-primary mapping-row-checkbox"
                           data-mapping-id="${rule.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <div class="font-medium">${utils.escapeHtml(rule.original_value)}</div>
                </td>
                <td>
                    <div class="max-w-xs truncate">${mappedValue}</div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="text-sm text-base-content/60">
                        ${utils.formatDateTime(rule.created_at)}
                    </div>
                </td>
                <td>
                    <div class="text-sm text-base-content/60">
                        ${utils.formatDateTime(rule.updated_at)}
                    </div>
                </td>
                <td>
                    <div class="flex gap-1">
                        <button class="btn btn-ghost btn-xs edit-mapping-btn" data-mapping-id="${rule.id}" title="编辑映射规则">
                            <i class="bi bi-pencil" aria-label="编辑图标"></i>
                        </button>
                        <button class="btn btn-ghost btn-xs text-error delete-mapping-btn" data-mapping-id="${rule.id}" title="删除映射规则">
                            <i class="bi bi-trash" aria-label="删除图标"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 绑定映射表格行事件
     */
    bindMappingRowEvents() {
        // 复选框事件
        document.querySelectorAll('.mapping-row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const mappingId = parseInt(e.target.getAttribute('data-mapping-id'));
                if (e.target.checked) {
                    this.selectedMappingIds.add(mappingId);
                } else {
                    this.selectedMappingIds.delete(mappingId);
                }
                this.updateMappingSelectionUI();
            });
        });

        // 编辑按钮事件
        document.querySelectorAll('.edit-mapping-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const mappingId = parseInt(e.target.closest('button').getAttribute('data-mapping-id'));
                this.showEditMappingModal(mappingId);
            });
        });

        // 删除按钮事件
        document.querySelectorAll('.delete-mapping-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const mappingId = parseInt(e.target.closest('button').getAttribute('data-mapping-id'));
                this.showDeleteMappingModal(mappingId);
            });
        });
    }

    /**
     * 更新映射选择UI
     */
    updateMappingSelectionUI() {
        const selectedCount = this.selectedMappingIds.size;
        const totalCount = this.mappingRules.length;

        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('mapping-select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = selectedCount > 0 && selectedCount === totalCount;
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalCount;
        }

        // 更新统计信息
        const selectedMappingsElement = document.getElementById('selected-mappings');
        if (selectedMappingsElement) {
            selectedMappingsElement.textContent = selectedCount;
        }

        // 显示/隐藏批量操作按钮（在下拉菜单中）
        const batchDeleteBtn = document.getElementById('batch-delete-mapping-btn');
        if (batchDeleteBtn) {
            const parentLi = batchDeleteBtn.closest('li');
            if (parentLi) {
                parentLi.style.display = selectedCount > 0 ? 'block' : 'none';
            }
        }
    }

    /**
     * 切换全选状态
     */
    toggleSelectAll(checked) {
        this.selectedMappingIds.clear();

        if (checked) {
            this.mappingRules.forEach(rule => {
                this.selectedMappingIds.add(rule.id);
            });
        }

        // 更新复选框状态
        document.querySelectorAll('.mapping-row-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });

        this.updateMappingSelectionUI();
    }

    /**
     * 更新映射统计信息
     */
    updateMappingStatistics() {
        const totalMappings = this.totalCount;
        // 根据新的业务逻辑：有映射值的是转换映射，无映射值的是删除规则
        const convertMappings = this.mappingRules.filter(rule => rule.mapped_value).length;
        const deleteMappings = this.mappingRules.filter(rule => !rule.mapped_value).length;

        // 更新统计显示
        const totalElement = document.getElementById('total-mappings');
        const activeElement = document.getElementById('active-mappings');
        const emptyElement = document.getElementById('empty-mappings');

        if (totalElement) totalElement.textContent = totalMappings;
        if (activeElement) activeElement.textContent = convertMappings;
        if (emptyElement) emptyElement.textContent = deleteMappings;

        // 更新计数信息
        const countInfo = document.getElementById('mapping-count-info');
        if (countInfo) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            countInfo.textContent = `共 ${totalMappings} 个${config ? config.name : ''}映射规则`;
        }
    }

    /**
     * 更新映射分页
     */
    updateMappingPagination() {
        const container = document.getElementById('mapping-pagination-container');
        if (!container) return;

        const totalPages = Math.ceil(this.totalCount / this.pageSize);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<div class="join">';

        // 上一页按钮
        const prevDisabled = this.currentPage === 1 ? 'btn-disabled' : '';
        paginationHtml += `
            <button class="join-item btn btn-sm ${prevDisabled}" data-page="${this.currentPage - 1}">
                <i class="bi bi-chevron-left" aria-label="上一页图标"></i>
            </button>
        `;

        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'btn-active' : '';
            paginationHtml += `
                <button class="join-item btn btn-sm ${activeClass}" data-page="${i}">
                    ${i}
                </button>
            `;
        }

        // 下一页按钮
        const nextDisabled = this.currentPage === totalPages ? 'btn-disabled' : '';
        paginationHtml += `
            <button class="join-item btn btn-sm ${nextDisabled}" data-page="${this.currentPage + 1}">
                <i class="bi bi-chevron-right" aria-label="下一页图标"></i>
            </button>
        `;

        paginationHtml += '</div>';
        container.innerHTML = paginationHtml;

        // 绑定分页事件
        container.querySelectorAll('button[data-page]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.target.closest('button').getAttribute('data-page'));
                if (page >= 1 && page <= totalPages && page !== this.currentPage) {
                    this.currentPage = page;
                    this.loadMappingRules();
                }
            });
        });
    }

    // ========== 状态管理方法 ==========

    /**
     * 显示映射加载状态
     */
    showMappingLoading() {
        const loadingState = document.getElementById('mapping-loading-state');
        if (loadingState) loadingState.classList.remove('hidden');
    }

    /**
     * 隐藏映射加载状态
     */
    hideMappingLoading() {
        const loadingState = document.getElementById('mapping-loading-state');
        if (loadingState) loadingState.classList.add('hidden');
    }

    /**
     * 显示映射空状态
     */
    showMappingEmptyState() {
        const emptyState = document.getElementById('mapping-empty-state');
        if (emptyState) emptyState.classList.remove('hidden');
    }

    /**
     * 隐藏映射空状态
     */
    hideMappingEmptyState() {
        const emptyState = document.getElementById('mapping-empty-state');
        if (emptyState) emptyState.classList.add('hidden');
    }

    /**
     * 显示映射错误状态
     */
    showMappingErrorState(message) {
        // 可以在这里添加错误状态显示逻辑
        console.error('映射加载错误:', message);
    }

    /**
     * 隐藏所有映射状态
     */
    hideMappingStates() {
        this.hideMappingLoading();
        this.hideMappingEmptyState();
    }

    // ========== 模态框处理方法 ==========

    /**
     * 显示添加映射模态框
     */
    showAddMappingModal() {
        this.currentMappingRule = null;

        const modal = document.getElementById('mapping-modal');
        const title = document.getElementById('mapping-modal-title');
        const form = document.getElementById('mapping-form');

        if (title) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            title.textContent = `添加${config ? config.name : ''}映射规则`;
        }

        if (form) form.reset();

        // 设置映射类型
        const mappingTypeInput = document.getElementById('mapping-type');
        if (mappingTypeInput) mappingTypeInput.value = this.currentMappingType;

        if (modal) modal.showModal();
    }

    /**
     * 显示编辑映射模态框
     */
    showEditMappingModal(mappingId) {
        const rule = this.mappingRules.find(r => r.id === mappingId);
        if (!rule) {
            console.error('未找到映射规则，ID:', mappingId);
            toast.error('未找到要编辑的映射规则');
            return;
        }

        this.currentMappingRule = rule;

        const modal = document.getElementById('mapping-modal');
        const title = document.getElementById('mapping-modal-title');
        const form = document.getElementById('mapping-form');

        if (title) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            title.textContent = `编辑${config ? config.name : ''}映射规则`;
        }

        // 填充表单数据
        const mappingIdInput = document.getElementById('mapping-id');
        const mappingTypeInput = document.getElementById('mapping-type');
        const originalValueInput = document.getElementById('mapping-original-value');
        const mappedValueInput = document.getElementById('mapping-mapped-value');

        if (mappingIdInput) mappingIdInput.value = rule.id;
        if (mappingTypeInput) mappingTypeInput.value = this.currentMappingType;
        if (originalValueInput) originalValueInput.value = rule.original_value;
        if (mappedValueInput) mappedValueInput.value = rule.mapped_value || '';

        if (modal) {
            modal.showModal();
        } else {
            console.error('模态框元素不存在');
        }
    }

    /**
     * 处理映射表单提交
     */
    async handleMappingFormSubmit() {
        const form = document.getElementById('mapping-form');
        const submitBtn = document.getElementById('mapping-submit-btn');
        const spinner = submitBtn?.querySelector('.loading');

        if (!form) return;

        // 获取表单数据
        const formData = new FormData(form);
        const data = {
            original_value: formData.get('original_value')?.trim(),
            mapped_value: formData.get('mapped_value')?.trim() || null
        };

        // 验证数据
        if (!data.original_value) {
            toast.error('请输入原始值');
            return;
        }

        // 显示加载状态
        if (submitBtn) submitBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            const mappingId = document.getElementById('mapping-id')?.value;

            if (mappingId && this.currentMappingRule) {
                // 更新映射规则
                await api.updateMappingRule(this.currentMappingType, parseInt(mappingId), data);
                toast.success('映射规则更新成功');
            } else {
                // 创建映射规则
                await api.createMappingRule(this.currentMappingType, data);
                toast.success('映射规则创建成功');
            }

            // 关闭模态框并刷新数据
            const modal = document.getElementById('mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('保存映射规则失败:', error);
            handleApiError(error, '保存映射规则');
        } finally {
            // 恢复按钮状态
            if (submitBtn) submitBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 显示删除映射确认模态框
     */
    showDeleteMappingModal(mappingId) {
        const rule = this.mappingRules.find(r => r.id === mappingId);
        if (!rule) return;

        this.currentMappingRule = rule;

        const modal = document.getElementById('delete-mapping-modal');
        const message = document.getElementById('delete-mapping-message');

        if (message) {
            message.textContent = `您确定要删除映射规则 "${rule.original_value}" 吗？`;
        }

        if (modal) modal.showModal();
    }

    /**
     * 处理删除映射
     */
    async handleDeleteMapping() {
        if (!this.currentMappingRule) return;

        const confirmBtn = document.getElementById('confirm-delete-mapping-btn');
        const spinner = confirmBtn?.querySelector('.loading');

        // 显示加载状态
        if (confirmBtn) confirmBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            await api.deleteMappingRule(this.currentMappingType, this.currentMappingRule.id);
            toast.success('映射规则删除成功');

            // 关闭模态框并刷新数据
            const modal = document.getElementById('delete-mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('删除映射规则失败:', error);
            handleApiError(error, '删除映射规则');
        } finally {
            // 恢复按钮状态
            if (confirmBtn) confirmBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 显示批量删除确认模态框
     */
    showBatchDeleteModal() {
        if (this.selectedMappingIds.size === 0) {
            toast.warning('请先选择要删除的映射规则');
            return;
        }

        const modal = document.getElementById('batch-delete-mapping-modal');
        const message = document.getElementById('batch-delete-mapping-message');

        if (message) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            message.textContent = `您确定要删除选中的 ${this.selectedMappingIds.size} 个${config ? config.name : ''}映射规则吗？`;
        }

        if (modal) modal.showModal();
    }

    /**
     * 处理批量删除映射
     */
    async handleBatchDeleteMapping() {
        if (this.selectedMappingIds.size === 0) return;

        const confirmBtn = document.getElementById('confirm-batch-delete-mapping-btn');
        const spinner = confirmBtn?.querySelector('.loading');

        // 显示加载状态
        if (confirmBtn) confirmBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            // 逐个删除选中的映射规则
            const deletePromises = Array.from(this.selectedMappingIds).map(id =>
                api.deleteMappingRule(this.currentMappingType, id)
            );

            await Promise.all(deletePromises);
            toast.success(`成功删除 ${this.selectedMappingIds.size} 个映射规则`);

            // 清空选择并关闭模态框
            this.selectedMappingIds.clear();
            const modal = document.getElementById('batch-delete-mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('批量删除映射规则失败:', error);
            handleApiError(error, '批量删除映射规则');
        } finally {
            // 恢复按钮状态
            if (confirmBtn) confirmBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    // ========== 导入导出功能 ==========

    /**
     * 显示导入模态框
     */
    showImportModal() {
        const modal = document.getElementById('import-mapping-modal');
        const fileInput = document.getElementById('mapping-file-input');
        const previewTextarea = document.getElementById('mapping-preview-textarea');
        const submitBtn = document.getElementById('import-mapping-submit-btn');

        // 重置表单
        if (fileInput) fileInput.value = '';
        if (previewTextarea) previewTextarea.value = '';
        if (submitBtn) submitBtn.disabled = true;

        if (modal) modal.showModal();
    }

    /**
     * 处理文件选择
     */
    async handleFileSelect(file) {
        const previewTextarea = document.getElementById('mapping-preview-textarea');
        const submitBtn = document.getElementById('import-mapping-submit-btn');

        if (!file) {
            if (previewTextarea) previewTextarea.value = '';
            if (submitBtn) submitBtn.disabled = true;
            return;
        }

        if (!file.name.toLowerCase().endsWith('.json')) {
            toast.error('请选择JSON格式的文件');
            return;
        }

        try {
            const text = await file.text();
            const data = JSON.parse(text);

            // 验证文件格式 - 支持多种格式
            let rules = [];
            if (Array.isArray(data.rules)) {
                // 标准格式：{ rules: [...] }
                rules = data.rules;
            } else if (Array.isArray(data)) {
                // 简单数组格式：[...]
                rules = data;
            } else {
                throw new Error('文件格式不正确，需要包含rules数组或直接为数组格式');
            }

            // 验证规则格式
            for (let i = 0; i < rules.length; i++) {
                const rule = rules[i];
                if (!rule.original && !rule.original_value) {
                    throw new Error(`第${i + 1}个规则缺少原始值字段(original或original_value)`);
                }
            }

            // 显示预览
            if (previewTextarea) {
                previewTextarea.value = JSON.stringify({ rules }, null, 2);
            }

            // 启用提交按钮
            if (submitBtn) submitBtn.disabled = false;

        } catch (error) {
            console.error('文件解析失败:', error);
            toast.error(`文件格式错误: ${error.message}`);

            if (previewTextarea) previewTextarea.value = '';
            if (submitBtn) submitBtn.disabled = true;
        }
    }

    /**
     * 处理导入提交
     */
    async handleImportSubmit() {
        const fileInput = document.getElementById('mapping-file-input');
        const overwriteCheckbox = document.getElementById('overwrite-mapping-checkbox');
        const submitBtn = document.getElementById('import-mapping-submit-btn');
        const spinner = submitBtn?.querySelector('.loading');

        if (!fileInput?.files[0]) {
            toast.error('请先选择文件');
            return;
        }

        // 显示加载状态
        if (submitBtn) submitBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            const file = fileInput.files[0];
            const text = await file.text();
            const data = JSON.parse(text);

            // 处理不同的JSON格式
            let rules = [];
            if (Array.isArray(data.rules)) {
                rules = data.rules;
            } else if (Array.isArray(data)) {
                rules = data;
            } else {
                throw new Error('无效的文件格式');
            }

            const importData = {
                rules: rules.map(rule => ({
                    original_value: rule.original || rule.original_value,
                    mapped_value: rule.mapped || rule.mapped_value || null
                })),
                overwrite: overwriteCheckbox?.checked || false
            };

            const response = await api.batchCreateMappingRules(this.currentMappingType, importData);

            toast.success(`导入完成：成功 ${response.success_count} 个，失败 ${response.failed_count} 个`);

            // 关闭模态框并刷新数据
            const modal = document.getElementById('import-mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('导入映射规则失败:', error);
            handleApiError(error, '导入映射规则');
        } finally {
            // 恢复按钮状态
            if (submitBtn) submitBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 导出映射规则
     */
    async exportMappingRules() {
        try {
            const response = await api.exportMappingRules(this.currentMappingType);

            // 创建下载链接
            const blob = new Blob([JSON.stringify(response, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentMappingType}_mappings_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            toast.success('映射规则导出成功');

        } catch (error) {
            console.error('导出映射规则失败:', error);
            handleApiError(error, '导出映射规则');
        }
    }
}

// 页面加载完成后初始化设置管理器
document.addEventListener('DOMContentLoaded', async () => {
    if (window.location.pathname === '/settings') {
        // 确保API客户端已加载
        if (typeof window.api === 'undefined') {
            setTimeout(() => {
                if (typeof window.api !== 'undefined') {
                    window.settingsManager = new SettingsManager();
                    window.settingsManager.init();
                }
            }, 100);
        } else {
            window.settingsManager = new SettingsManager();
            await window.settingsManager.init();
        }
    }
});
