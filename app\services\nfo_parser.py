"""
NFO 文件解析器
用于解析 Kodi/Jellyfin 格式的 NFO 文件并提取电影元数据
"""
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


class NFOParser:
    """NFO 文件解析器类"""
    
    @staticmethod
    def parse_nfo_file(nfo_path: str) -> Optional[Dict[str, Any]]:
        """
        解析 NFO 文件并返回结构化的电影数据
        
        Args:
            nfo_path: NFO 文件路径
            
        Returns:
            包含电影元数据的字典，如果解析失败则返回 None
        """
        try:
            if not Path(nfo_path).exists():
                logger.error(f"NFO 文件不存在: {nfo_path}")
                return None
                
            tree = ET.parse(nfo_path)
            root = tree.getroot()
            
            if root.tag != 'movie':
                logger.error(f"NFO 文件格式错误，根元素不是 'movie': {nfo_path}")
                return None
                
            return NFOParser._extract_movie_data(root)
            
        except ET.ParseError as e:
            logger.error(f"NFO 文件解析错误 {nfo_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"解析 NFO 文件时发生未知错误 {nfo_path}: {e}")
            return None
    
    @staticmethod
    def _extract_movie_data(root: ET.Element) -> Dict[str, Any]:
        """
        从 XML 根元素中提取电影数据
        
        Args:
            root: XML 根元素
            
        Returns:
            包含电影元数据的字典
        """
        movie_data = {}
        
        # 基本文本字段
        text_fields = {
            'title': 'title',
            'originaltitle': 'original_title',
            'plot': 'plot',
            'outline': 'outline',
            'country': 'country',
            'sorttitle': 'sort_title',
            'trailer': 'trailer',
            'num': 'num'
        }
        
        for xml_field, db_field in text_fields.items():
            element = root.find(xml_field)
            if element is not None and element.text:
                movie_data[db_field] = element.text.strip()
        
        # 数值字段
        numeric_fields = {
            'year': 'year',
            'runtime': 'runtime',
            'rating': 'rating',
            'criticrating': 'critic_rating'
        }
        
        for xml_field, db_field in numeric_fields.items():
            element = root.find(xml_field)
            if element is not None and element.text:
                try:
                    if xml_field in ['rating', 'criticrating']:
                        movie_data[db_field] = float(element.text.strip())
                    else:
                        movie_data[db_field] = int(element.text.strip())
                except ValueError:
                    logger.warning(f"无法转换数值字段 {xml_field}: {element.text}")
        
        # 布尔字段
        lockdata_element = root.find('lockdata')
        if lockdata_element is not None and lockdata_element.text:
            movie_data['lock_data'] = lockdata_element.text.strip().lower() == 'true'
        
        # 日期字段
        date_fields = {
            'dateadded': 'date_added',
            'premiered': 'premiered',
            'releasedate': 'release_date'
        }
        
        for xml_field, db_field in date_fields.items():
            element = root.find(xml_field)
            if element is not None and element.text:
                movie_data[db_field] = NFOParser._parse_date(element.text.strip())
        
        # 多值字段
        movie_data['genres'] = NFOParser._extract_list_values(root, 'genre')
        movie_data['tags'] = NFOParser._extract_list_values(root, 'tag')
        movie_data['studios'] = NFOParser._extract_list_values(root, 'studio')
        
        # 演员信息
        movie_data['actors'] = NFOParser._extract_actors(root)
        
        # 系列信息
        series_element = root.find('set')
        if series_element is not None:
            series_name_element = series_element.find('name')
            if series_name_element is None:
                series_name_element = series_element.find('n')
            if series_name_element is not None and series_name_element.text:
                movie_data['series_name'] = series_name_element.text.strip()
        
        return movie_data
    
    @staticmethod
    def _extract_list_values(root: ET.Element, tag_name: str) -> List[str]:
        """
        提取多个相同标签的值
        
        Args:
            root: XML 根元素
            tag_name: 标签名称
            
        Returns:
            值的列表
        """
        values = []
        for element in root.findall(tag_name):
            if element.text:
                values.append(element.text.strip())
        return values
    
    @staticmethod
    def _extract_actors(root: ET.Element) -> List[Dict[str, str]]:
        """
        提取演员信息

        Args:
            root: XML 根元素

        Returns:
            演员信息列表
        """
        actors = []
        for actor_element in root.findall('actor'):
            actor_data = {}

            # NFO 文件中演员姓名可能使用 <name> 或 <n> 标签
            name_element = actor_element.find('name')
            if name_element is None:
                name_element = actor_element.find('n')
            if name_element is not None and name_element.text:
                actor_data['name'] = name_element.text.strip()

            role_element = actor_element.find('role')
            if role_element is not None and role_element.text:
                actor_data['role'] = role_element.text.strip()

            type_element = actor_element.find('type')
            if type_element is not None and type_element.text:
                actor_data['actor_type'] = type_element.text.strip()

            if 'name' in actor_data:  # 只有当有演员姓名时才添加
                actors.append(actor_data)

        return actors
    
    @staticmethod
    def _parse_date(date_string: str) -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_string: 日期字符串
            
        Returns:
            datetime 对象，如果解析失败则返回 None
        """
        date_formats = [
            '%Y-%m-%d %H:%M:%S',  # 2021-12-15 10:32:06
            '%Y-%m-%d',           # 2002-04-15
            '%Y'                  # 2002
        ]
        
        for date_format in date_formats:
            try:
                return datetime.strptime(date_string, date_format)
            except ValueError:
                continue
        
        logger.warning(f"无法解析日期格式: {date_string}")
        return None
